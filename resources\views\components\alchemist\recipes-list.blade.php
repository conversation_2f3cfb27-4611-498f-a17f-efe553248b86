@props(['userRecipes' => [], 'selectedRecipeId' => null])

{{--
Компонент списка рецептов алхимика
Отображает доступные рецепты для варки зелий
--}}

<div class="mb-4">
    <h3 class="text-[#e5b769] font-semibold text-base mb-2 border-b border-[#3d3a2e] pb-1">Доступные рецепты</h3>
    
    @if($userRecipes && count($userRecipes) > 0)
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
            @foreach($userRecipes as $userRecipe)
                @php
                    $recipe = $userRecipe->recipe;
                    $isSelected = $selectedRecipeId && $selectedRecipeId == $recipe->id;
                @endphp
                
                <div class="recipe-card cursor-pointer transition-all duration-300 
                    {{ $isSelected ? 'ring-2 ring-[#a6925e] bg-gradient-to-b from-[#3d3a2e] to-[#2a2721]' : 'bg-gradient-to-b from-[#2a2722] to-[#1d1916] hover:from-[#3d3a2e] hover:to-[#2a2721]' }} 
                    border border-[#514b3c] rounded-md p-2"
                    data-recipe-id="{{ $recipe->id }}"
                    data-recipe-name="{{ $recipe->name }}"
                    data-brewing-time="{{ $recipe->brewing_time }}"
                    data-experience="{{ $recipe->experience_reward }}"
                    data-ingredients="{{ json_encode($recipe->ingredients->map(function($ingredient) {
                        return [
                            'id' => $ingredient->id,
                            'name' => $ingredient->name,
                            'quantity' => $ingredient->pivot->quantity ?? 1,
                            'icon' => $ingredient->icon_path ?? 'assets/ingredients/default.png'
                        ];
                    })) }}">
                    
                    {{-- Заголовок рецепта --}}
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="text-sm font-medium text-[#e5b769] truncate">{{ $recipe->name }}</h4>
                        <span class="text-xs text-[#d3c6a6] bg-[#1a1814] px-1 py-0.5 rounded">
                            x{{ $userRecipe->quantity }}
                        </span>
                    </div>
                    
                    {{-- Описание рецепта --}}
                    @if($recipe->description)
                        <p class="text-xs text-[#d3c6a6] mb-2 line-clamp-2">{{ $recipe->description }}</p>
                    @endif
                    
                    {{-- Информация о рецепте --}}
                    <div class="text-xs text-[#998d66] space-y-1 mb-2">
                        <div class="flex justify-between">
                            <span>Время варки:</span>
                            <span class="text-[#d3c6a6]">{{ $recipe->brewing_time }} мин</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Опыт:</span>
                            <span class="text-[#c1a96e]">+{{ $recipe->experience_reward }}</span>
                        </div>
                        @if($recipe->required_level > 1)
                            <div class="flex justify-between">
                                <span>Требуемый уровень:</span>
                                <span class="text-[#e5b769]">{{ $recipe->required_level }}</span>
                            </div>
                        @endif
                    </div>
                    
                    {{-- Ингредиенты --}}
                    <div class="border-t border-[#3d3a2e] pt-2">
                        <div class="text-xs text-[#998d66] mb-1">Ингредиенты:</div>
                        <div class="flex flex-wrap gap-1">
                            @foreach($recipe->ingredients as $ingredient)
                                <div class="flex items-center bg-[#1a1814] rounded px-1 py-0.5 border border-[#3d3a2e]">
                                    @if($ingredient->icon_path)
                                        <img src="{{ asset($ingredient->icon_path) }}" 
                                             alt="{{ $ingredient->name }}" 
                                             class="w-3 h-3 mr-1"
                                             onerror="this.src='{{ asset('assets/ingredients/default.png') }}'">
                                    @endif
                                    <span class="text-xs text-[#d3c6a6]">
                                        {{ $ingredient->pivot->quantity ?? 1 }}x {{ $ingredient->name }}
                                    </span>
                                </div>
                            @endforeach
                        </div>
                    </div>
                    
                    {{-- Индикатор выбора --}}
                    @if($isSelected)
                        <div class="mt-2 text-center">
                            <span class="text-xs text-[#c1a96e] bg-[#1a1814] px-2 py-1 rounded border border-[#a6925e]">
                                ✓ Выбран
                            </span>
                        </div>
                    @endif
                </div>
            @endforeach
        </div>
    @else
        {{-- Сообщение об отсутствии рецептов --}}
        <div class="bg-gradient-to-b from-[#2a2722] to-[#1d1916] border border-[#514b3c] rounded-md p-4 text-center">
            <div class="text-[#8a8a8a] text-sm">
                <div class="text-2xl mb-2">📜</div>
                <div>У вас нет рецептов для алхимии</div>
                <div class="text-xs mt-1">Найдите или купите рецепты у торговцев</div>
            </div>
        </div>
    @endif
</div>

{{-- Стили для ограничения текста --}}
<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>

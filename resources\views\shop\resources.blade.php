<!DOCTYPE html>
<html lang="en">
<?php use Illuminate\Support\Facades\Auth; ?>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Регистрация</title>

    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>

<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif min-h-screen flex flex-col">
    {{-- Основной контейнер --}}
    <div
        class="flex-grow container max-w-screen-lg mx-auto px-1 py-0  bg-gradient-to-b from-[#4a4a3d] to-[#3b3a33] border-2 border-[#a6925e] rounded-lg shadow-lg">

        {{-- Сообщение приветствия --}}
        @if (session('welcome_message'))
            <div class="bg-[#3b3a33] text-white p-4 rounded mb-4 text-center">
                {{ session('welcome_message') }}
            </div>
        @endif

        {{-- Верхняя панель --}}
        <div class="flex justify-between items-center text-[#d9d3b8]  rounded-md pt-1.5 shadow-inner">
            {{-- HP с иконкой --}}
            <div class="flex items-center">
                <div
                    class="w-6 h-6 flex items-center justify-center bg-[#38352c] rounded-full border border-[#a6925e] mr-1">
                    <span class="text-[#FF6347] text-xs">❤️</span>
                </div>
                <div class="flex flex-col">
                    <div class="w-16 bg-[#3b3a33] h-1.5 rounded-full overflow-hidden">
                        <div class="bg-gradient-to-r from-[#8B0000] to-[#FF6347] h-full rounded-full"
                            style="width: calc({{ $actualResources['current_hp'] }}/{{ $userProfile->max_hp }}*100%)">
                        </div>
                    </div>
                    <span
                        class="text-[#e5b769] text-[12px]">{{ $actualResources['current_hp'] }}/{{ $userProfile->max_hp }}</span>
                </div>
            </div>

            {{-- Уведомления --}}
            <div class="flex space-x-1">
                {{-- Почта --}}
                <a href="/mail" class="relative group">
                    <div
                        class="flex items-center justify-center w-6 h-6 bg-[#38352c] rounded-full border border-[#a6925e] hover:bg-[#4a452c] transition-colors duration-300">
                        <span class="block text-xs">📩</span>
                    </div>
                    <span
                        class="absolute -top-1 -right-1 bg-[#e74c3c] text-white text-[8px] rounded-full w-3 h-3 flex items-center justify-center">
                        3
                    </span>
                </a>

                {{-- События --}}
                <a href="/events" class="relative group">
                    <div
                        class="flex items-center justify-center w-6 h-6 bg-[#38352c] rounded-full border border-[#a6925e] hover:bg-[#4a452c] transition-colors duration-300">
                        <span class="block text-xs">🎉</span>
                    </div>
                    <span
                        class="absolute -top-1 -right-1 bg-[#e74c3c] text-white text-[8px] rounded-full w-3 h-3 flex items-center justify-center">
                        3
                    </span>
                </a>

                {{-- Задания --}}
                <a href="/quests" class="relative group">
                    <div
                        class="flex items-center justify-center w-6 h-6 bg-[#38352c] rounded-full border border-[#a6925e] hover:bg-[#4a452c] transition-colors duration-300">
                        <span class="block text-xs">📜</span>
                    </div>
                    <span
                        class="absolute -top-1 -right-1 bg-[#e74c3c] text-white text-[8px] rounded-full w-3 h-3 flex items-center justify-center">
                        5
                    </span>
                </a>
            </div>

            {{-- MP с иконкой --}}
            <div class="flex items-center">
                <div class="flex flex-col items-end">
                    <div class="w-16 bg-[#3b3a33] h-1.5 rounded-full overflow-hidden">
                        <div class="bg-gradient-to-r from-[#00008B] to-[#1E90FF] h-full rounded-full"
                            style="width: calc({{ $userProfile->mp }}/{{ $userProfile->max_mp }}*100%)"></div>
                    </div>
                    <span class="text-[#e5b769] text-[12px]">{{ $userProfile->mp }}/{{ $userProfile->max_mp }}</span>
                </div>
                <div
                    class="w-6 h-6 flex items-center justify-center bg-[#38352c] rounded-full border border-[#a6925e] ml-1">
                    <span class="text-[#1E90FF] text-xs">🔮</span>
                </div>
            </div>
        </div>

        {{-- Шкала прогресса --}}
        <div class="w-full bg-[#6b6658] mt-0.5 h-0.5 rounded-full mb-5">
            <div class="bg-[#e5b769] h-0.5 rounded-full" style="width: 50%;"></div>
            <div class="flex gap-1 items-center ">
                {{-- Золото --}}
                <div class="flex items-center ">
                    <img src="{{ asset('assets/goldIcon.png') }}" alt="Золото" class="w-4 h-4">
                    <span class="text-sm font-medium text-[#e5b769]">
                        {{ number_format($userProfile->gold, 0, ',', ' ') }}
                    </span>
                </div>
                {{-- Серебро --}}
                <div class="flex items-center ">
                    <img src="{{ asset('assets/silverIcon.png') }}" alt="Серебро" class="w-4 h-4">
                    <span class="text-sm font-medium text-[#c0c0c0]">
                        {{ number_format($userProfile->silver, 0, ',', ' ') }}
                    </span>
                </div>
                {{-- Бронза --}}
                <div class="flex items-center ">
                    <img src="{{ asset('assets/bronzeIcon.png') }}" alt="Бронза" class="w-4 h-4">
                    <span class="text-sm font-medium text-[#cd7f32]">
                        {{ number_format($userProfile->bronze, 0, ',', ' ') }}
                    </span>
                </div>
            </div>

        </div>
        {{-- Заголовок магазина --}}
        <x-breadcrumbs :breadcrumbs="$breadcrumbs" />
        <x-layout.location-name title="Магазин" />

        {{-- Баннер магазина --}}
        <div
            class="w-full h-20 bg-[#3b3a33] rounded-lg mt-4 flex items-center justify-center border border-[#a6925e] shadow-lg">
            <img src="{{ asset('assets/bannerShop.jpg') }}" alt="Изображение локации"
                class="w-full h-full object-cover rounded-lg opacity-75">
        </div>

        {{-- Кнопки переключения (Вещи, Рецепты, Ресурсы) --}}
        {{-- Берём стили Вкладок из "эталона", но маршруты/логику — из второго шаблона --}}
        <div class="flex flex-wrap justify-center gap-3 mb-6 mt-2">
            {{-- Вещи --}}
            <a href="{{ route('shop.index') }}"
                class="bg-[#c4a76d] text-[#2f2d2b] py-2 px-2 rounded hover:bg-[#d4b781] transition duration-300  ">
                Вещи
            </a>

            {{-- Рецепты (бывшие Зелья) --}}
            <a href="{{ route('shop.recipes') }}"
                class="bg-[#c4a76d] text-[#2f2d2b] py-2 px-2 rounded hover:bg-[#d4b781] transition duration-300    ">
                Рецепты
            </a>

            {{-- Ресурсы --}}
            <a href="{{ route('shop.resources') }}" class="bg-[#a6925e] text-[#2f2d2b] py-2 px-4 rounded shadow-inner border border-[#8b7c4b] 
                      focus:ring-2 focus:ring-offset-2 focus:ring-[#d4b781]">
                Ресурсы
            </a>
        </div>

        {{-- Фильтры (берём оформление из «эталона», а поля — из второго шаблона) --}}
        <div class="container mx-auto px-4 py-4 mb-4 
            bg-gradient-to-b from-[#4a4a3d] to-[#3b3a33] 
            rounded-md shadow-md">
            <form id="filterForm" method="GET" action="">
                {{-- Сетка фильтров --}}
                <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
                    {{-- Тип --}}
                    <div class="flex flex-col">
                        <label for="itemType" class="text-sm font-medium text-[#d9d3b8] mb-1">Тип:</label>
                        <select id="itemType" name="type" class="bg-[#6b6658] text-[#f5f5f5] py-1 px-3 rounded-md text-xs 
                               focus:outline-none focus:ring-2 focus:ring-[#807c70]">
                            <option value="">Все</option>
                            <option value="weapon">Оружие</option>
                            <option value="helmet">Шлем</option>
                            <option value="armor">Броня</option>
                            <option value="shield">Щит</option>
                        </select>
                    </div>

                    {{-- Качество --}}
                    <div class="flex flex-col">
                        <label for="quality" class="text-sm font-medium text-[#d9d3b8] mb-1">Качество:</label>
                        <select id="quality" name="quality" class="bg-[#6b6658] text-[#f5f5f5] py-1 px-3 rounded-md text-xs 
                               focus:outline-none focus:ring-2 focus:ring-[#807c70]">
                            <option value="">Все</option>
                            <option value="common">Обычное</option>
                            <option value="rare">Редкое</option>
                            <option value="magic">Магическое</option>
                            <option value="epic">Эпическое</option>
                            <option value="unique">Уникальное</option>
                            <option value="divine">Божественное</option>
                        </select>
                    </div>

                    {{-- Атрибут --}}
                    <div class="flex flex-col">
                        <label for="attribute" class="text-sm font-medium text-[#d9d3b8] mb-1">Атрибут:</label>
                        <select id="attribute" name="attribute" class="bg-[#6b6658] text-[#f5f5f5] py-1 px-3 rounded-md text-xs 
                               focus:outline-none focus:ring-2 focus:ring-[#807c70]">
                            <option value="">Все</option>
                            <option value="hp">ХП</option>
                            <option value="mp">МП</option>
                            <option value="strength">Сила</option>
                            <option value="intelligence">Интелект</option>
                        </select>
                    </div>

                    {{-- Цена --}}
                    <div class="flex flex-col">
                        <label for="price" class="text-sm font-medium text-[#d9d3b8] mb-1">Цена:</label>
                        <select id="price" name="price" class="bg-[#6b6658] text-[#f5f5f5] py-1 px-3 rounded-md text-xs 
                               focus:outline-none focus:ring-2 focus:ring-[#807c70]">
                            <option value="">Все</option>
                            <option value="bronze">Бронза</option>
                            <option value="silver">Серебро</option>
                            <option value="gold">Золото</option>
                        </select>
                    </div>
                </div>

                {{-- Кнопки фильтра --}}
                <div class="flex justify-center gap-2 mt-4">
                    <button type="submit" class="bg-[#c4a76d] text-[#2f2d2b] py-2 px-6 rounded 
                           hover:bg-[#d4b781] transition duration-300">
                        Применить
                    </button>
                    <button type="reset" class="bg-[#6b6658] text-[#f5f5f5] py-2 px-3 rounded 
                           hover:bg-[#807c70] transition duration-300">
                        Сбросить
                    </button>
                </div>
            </form>
        </div>

        {{-- Список товаров (сетка) --}}
        <div class="mt-6">
            <h1 class="text-3xl font-bold text-[#e5b769] mb-6 text-center">Список товаров</h1>

            @if($shopItems->isEmpty())
                <p class="text-gray-500 text-center">Товаров в магазине нет.</p>
            @else
                {{-- Сетка карточек (как в «эталоне») --}}
                <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    @foreach ($shopItems as $shopItem)
                        @php
                            $item = $shopItem->item;

                            // Определение класса для эффекта свечения в зависимости от качества
                            $qualityGlowMap = [
                                'Обычное' => '', // Обычные предметы не светятся
                                'Необычное' => 'animate-breathing-glow glow-color-green-400',
                                'Редкое' => 'animate-breathing-glow glow-color-blue-400',
                                'Эпическое' => 'animate-breathing-glow glow-color-purple-400',
                                'Легендарное' => 'animate-breathing-glow glow-color-orange-400',
                            ];
                            $qualityGlow = $qualityGlowMap[$item->quality] ?? '';
                        @endphp
                        <div class="bg-[#3b3a33] border border-[#a6925e] rounded-lg p-4 shadow-lg flex flex-col">
                            {{-- Иконка и данные предмета --}}
                            <div class="flex items-center mb-4">
                                <div class="relative">
                                    <img src="{{ asset($item->icon ?? 'assets/placeholder.png') }}" alt="Иконка предмета"
                                        class="w-12 h-12 object-cover rounded {{ $qualityGlow }}">
                                    {{-- Пример, если нужно показать «+50» --}}
                                    <p class="text-xs font-bold text-green-400 absolute -bottom-4 left-0">
                                        +50
                                    </p>
                                </div>
                                <div class="ml-3">
                                    <h2 class="text-sm font-bold text-[#e5b769]">{{ $item->name }}</h2>
                                    <p class="text-sm text-[#d9d3b8]">
                                        Цена:
                                        @if ($shopItem->price_gold > 0)
                                            {{ $shopItem->price_gold }} золота
                                        @endif
                                        @if ($shopItem->price_silver > 0)
                                            {{ $shopItem->price_silver }} серебра
                                        @endif
                                        @if ($shopItem->price_bronze > 0)
                                            {{ $shopItem->price_bronze }} бронзы
                                        @endif
                                    </p>
                                    <p class="text-sm text-[#d9d3b8]">
                                        {{ $item->level_required }} ур.
                                    </p>
                                </div>
                            </div>
                            {{-- Кнопки --}}
                            <div class="mt-auto flex space-x-2">
                                <button
                                    class="bg-[#c4a76d] text-[#2f2d2b] px-3 py-1 text-sm rounded hover:bg-[#d4b781] transition duration-300">
                                    Купить
                                </button>
                                <button
                                    class="bg-[#c4a76d] text-[#2f2d2b] px-3 py-1 text-sm rounded hover:bg-[#d4b781] transition duration-300">
                                    Подробнее
                                </button>
                            </div>
                        </div>
                    @endforeach
                </div>
            @endif
        </div>

        {{-- Пагинация --}}
        <div class="mt-6 text-center mb-4">
            {{ $shopItems->links('pagination::bootstrap-4') }}
        </div>

    </div> {{-- Закрываем .flex-grow container ... --}}

    {{-- Нижняя панель (Рюкзак, Персонаж, Гильдия) --}}
    <div class="container mx-auto text-center px-2 py-2 flex justify-center space-x-2">
        {{-- Рюкзак --}}
        <a href="{{ route('inventory.index') }}"
            class="bg-[#c4a76d] text-[#2f2d2b] py-1 px-1 rounded shadow-lg hover:bg-[#d4b781] transition duration-300">
            Рюкзак
        </a>
        {{-- Персонаж --}}
        <a href="{{ route('user.profile') }}"
            class="bg-[#c4a76d] text-[#2f2d2b] py-1 px-1 rounded shadow-lg hover:bg-[#d4b781] transition duration-300">
            Персонаж
        </a>
        {{-- Гильдия --}}
        <a href="#"
            class="bg-[#c4a76d] text-[#2f2d2b] py-1 px-1 rounded shadow-lg hover:bg-[#d4b781] transition duration-300">
            Гильдия
        </a>
    </div>

    {{-- Футер --}}
    <x-layout.footer :onlineCount="$onlineCount" />
</body>

</html>
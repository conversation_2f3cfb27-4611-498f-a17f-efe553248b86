<!DOCTYPE html>
<html lang="ru">
<?php
use Illuminate\Support\Facades\Auth;
use App\Models\GuildInvitation;
?>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Битва - Echoes of Eternity</title>

    @vite(['resources/css/app.css', 'resources/js/app.js', 'resources/js/battle/tooltips.js', 'resources/css/battle/tooltips.css', 'resources/css/components/guild-invitation.css', 'resources/css/components/donation-button.css', 'resources/css/battle/main.css'])
</head>

<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif min-h-screen flex flex-col">
    {{-- Основной контейнер --}}
    <div class="container max-w-md mx-auto px-1 py-0 bg-gradient-to-b from-[#4a4a3d] to-[#3b3a33] border-2 border-[#a6925e] rounded-lg flex-grow"
        style="background: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.0))">

        {{-- HP/MP блок с уведомлениями --}}
        <x-layout.hp-mp-bar :actualResources="$actualResources" :userProfile="$userProfile">
            {{-- Слот для уведомлений между HP и MP --}}
            <x-layout.notifications-bar :hasUnreadMessages="$hasUnreadMessages ?? false"
                :unreadMessagesCount="$unreadMessagesCount ?? 0" :hasBrokenItems="$hasBrokenItems ?? false"
                :brokenItemsCount="$brokenItemsCount ?? 0" />
        </x-layout.hp-mp-bar>


        {{-- Отображение валюты с прогрессом опыта --}}
        <x-layout.currency-display :userProfile="$userProfile" :experienceProgress="$experienceProgress ?? null" />

        {{-- Уведомление о приглашении в гильдию --}}
        @if(Auth::check())
            @php
                $guildInvitation = Auth::user()->getLatestGuildInvitation();
            @endphp
            <x-layout.guild-invitation :guildInvitation="$guildInvitation" />
        @endif

        {{-- Блок изображения локации с хлебными крошками и названием --}}
        <x-layout.home-location-image :breadcrumbs="$breadcrumbs" title="Арена битв"
            imagePath="assets/bannersBg/bannerHome.jpg" imageAlt="Арена битв">
            {{-- Приветственное сообщение внутри блока изображения --}}
            <x-battle.welcome-message />
        </x-layout.home-location-image>

        {{-- Flash-сообщения --}}
        <x-game-flash-messages />

        {{-- Основной контентный блок --}}
        <div class="bg-[#211f1a] px-2.5 py-3 shadow-inner">
            {{-- Вводная информация о битве --}}
            <div class="mb-4 text-sm text-[#d3c6a6] border-l-2 border-[#8c784e] pl-2">
                <p>Добро пожаловать на арену битв! Выберите локацию для сражений и испытайте свои силы в бою против
                    могущественных противников.</p>
            </div>

            {{-- Основной контент - Меню боевых локаций --}}
            @php
                $isStunned = Auth::user()->isStunned();
            @endphp
            <x-battle.location-menu :isStunned="$isStunned" />
        </div>
    </div>

    {{-- Нижние кнопки навигации --}}
    @php
        $isStunned = Auth::user()->isStunned();
    @endphp
    <x-layout.navigation-buttons :isDisabled="$isStunned" />

    {{-- Футер --}}
    <x-layout.footer :onlineCount="$onlineCount" />

    {{-- Мобильные всплывающие подсказки --}}
    <x-battle.tooltip-modal id="dungeon-tooltip" title="Подземелье" icon="assets/iconDungeon.png"
        description="Исследуйте древние подземелья, сражайтесь с боссами и находите легендарные сокровища."
        releaseDate="Ожидаемая дата выхода: скоро" />

    <x-battle.tooltip-modal id="trial-tooltip" title="Испытание" icon="assets/iconTrial.png"
        description="Пройдите серию испытаний, проверьте свои навыки и получите уникальные награды за достижения."
        releaseDate="Ожидаемая дата выхода: следующее обновление" />

    <x-battle.tooltip-modal id="dominions-tooltip" title="Доминионы" icon="assets/iconDominions.png"
        description="Масштабные сражения за территории между гильдиями. Захватывайте замки, собирайте дань и расширяйте свое влияние."
        releaseDate="Ожидаемая дата выхода: крупное обновление (Q3 2025)" :requirements="[
        ['icon' => '👤', 'text' => 'Уровень персонажа 30+'],
        ['icon' => '🏰', 'text' => 'Членство в гильдии']
    ]" />

    <x-battle.tooltip-modal id="events-tooltip" title="Временные события" icon="assets/iconTemporary.png"
        description="Особые ограниченные по времени события с уникальными противниками, заданиями и наградами. Не пропустите!"
        releaseDate="Первое событие: Праздник Зимнего Солнцестояния (декабрь 2025)" plannedEvents="8" />

</body>

</html>
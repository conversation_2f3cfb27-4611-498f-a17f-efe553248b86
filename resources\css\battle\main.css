/**
 * Основные стили для страницы битвы (/battle)
 * Унифицированы с общим RPG стилем проекта в духе dark fantasy
 * Цветовая схема соответствует /masters/alchemist, /top-players, /dungeons
 */

/* Анимации для плавного появления элементов */
@keyframes fadeIn {
    0% {
        opacity: 0;
        transform: translateY(10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

.animate-pulse {
    animation: pulse 2s infinite ease-in-out;
}

/* Стили для боевых локаций */
.battle-location-container {
    @apply bg-gradient-to-b from-[#2a2722] to-[#1d1916] border border-[#514b3c] rounded-md shadow-lg;
}

.battle-location-header {
    @apply bg-gradient-to-r from-[#3d3a2e] to-[#2a2721] py-2 px-3 border-b border-[#514b3c];
}

.battle-location-title {
    @apply text-center text-[#e4d7b0] font-semibold text-sm tracking-wider uppercase;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

/* Стили для элементов локаций */
.battle-location-item {
    @apply flex items-center text-left pl-0 pr-3 border-b border-[#3b3629] transition-all duration-300;
}

.battle-location-item-active {
    @apply text-[#d4cbb0] bg-gradient-to-r from-[#1d1916] to-[#2a2722];
}

.battle-location-item-active:hover {
    @apply from-[#2a2722] to-[#3b3629] text-[#e4d7b0];
    box-shadow: inset 0 0 10px rgba(193, 169, 110, 0.2);
}

.battle-location-item-inactive {
    @apply text-[#998d66] bg-gradient-to-r from-[#1d1916] to-[#2a2722];
}

.battle-location-item-inactive:hover {
    @apply from-[#2a2722] to-[#1d1916] text-[#c1a96e];
}

.battle-location-item-blocked {
    @apply text-[#998d66] bg-gradient-to-r from-[#1d1916] to-[#2a2722];
}

/* Стили для иконок локаций */
.battle-location-icon {
    @apply w-12 h-12 flex items-center justify-center mr-0 bg-gradient-to-br from-[#2a2722] to-[#1d1916] border-r border-[#3b3629] transition-all duration-300;
}

.battle-location-icon-active {
    @apply text-[#c1a96e];
}

.battle-location-icon-active:hover {
    box-shadow: 0 0 8px rgba(193, 169, 110, 0.4);
}

.battle-location-icon-inactive {
    @apply text-[#998d66];
}

.battle-location-icon-blocked {
    @apply text-[#998d66];
}

/* Стили для изображений иконок */
.battle-location-image {
    @apply w-8 h-8 transition-all duration-300;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.8));
}

.battle-location-image-active {
    filter: drop-shadow(0 0 4px rgba(193, 169, 110, 0.5));
}

.battle-location-image-active:hover {
    filter: drop-shadow(0 0 6px rgba(193, 169, 110, 0.7));
}

.battle-location-image-inactive {
    @apply grayscale opacity-60;
}

.battle-location-image-blocked {
    @apply grayscale opacity-40;
}

/* Стили для текста локаций */
.battle-location-text {
    @apply font-medium tracking-wide ml-3 transition-all duration-300;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

/* Стили для бейджей статуса */
.battle-status-badge {
    @apply inline-flex items-center px-2 py-1 rounded-sm text-xs border relative overflow-hidden shadow-[0_2px_4px_rgba(0,0,0,0.5)] transition-all duration-300;
}

.battle-status-badge-development {
    @apply bg-gradient-to-r from-[#2f473c] to-[#1e2e27] text-[#f8eac2] border-[#3b3629];
}

.battle-status-badge-maintenance {
    @apply bg-gradient-to-r from-[#6e3f35] to-[#3c221b] text-[#f8eac2] border-[#3b3629];
}

/* Стили для приветственного сообщения */
.battle-welcome-message {
    @apply bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] border-2 border-[#514b3c] text-[#e4d7b0] p-3 rounded-lg shadow-lg w-full animate-fade-in;
}

.battle-welcome-header {
    @apply flex items-center justify-center mb-2;
}

.battle-welcome-icon {
    @apply text-[#c1a96e] text-lg;
    filter: drop-shadow(0 0 4px rgba(193, 169, 110, 0.4));
}

.battle-welcome-title {
    @apply mx-2 text-[#c1a96e] font-semibold text-sm tracking-wide;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

.battle-welcome-text {
    @apply text-sm text-center leading-relaxed;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

/* Стили для модальных окон подсказок */
.battle-tooltip-modal {
    @apply fixed inset-0 bg-black bg-opacity-85 z-50 hidden items-center justify-center;
    backdrop-filter: blur(2px);
}

.battle-tooltip-content {
    @apply relative w-11/12 max-w-sm mx-auto bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] border-2 border-[#514b3c] rounded-lg shadow-2xl overflow-hidden animate-fade-in;
}

.battle-tooltip-header {
    @apply bg-gradient-to-r from-[#2a2722] to-[#3d3a2e] py-3 px-4 border-b border-[#514b3c] flex items-center justify-between;
}

.battle-tooltip-title {
    @apply text-[#e4d7b0] text-base font-semibold tracking-wide;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

.battle-tooltip-close {
    @apply bg-gradient-to-br from-[#6e3f35] to-[#3c221b] text-[#f8eac2] text-lg w-7 h-7 flex items-center justify-center rounded-full border border-[#3b3629] transition-all duration-300;
}

.battle-tooltip-close:hover {
    @apply from-[#3c221b] to-[#6e3f35];
    box-shadow: 0 0 8px rgba(193, 169, 110, 0.4);
}

/* Стили для разделителей */
.battle-divider {
    @apply px-3 py-1 bg-gradient-to-r from-[#1d1916] to-[#2a2722];
}

.battle-divider-line {
    @apply h-px bg-gradient-to-r from-transparent via-[#c1a96e] to-transparent opacity-60 shadow-sm;
}

.battle-divider-text {
    @apply text-center mt-1 text-[#998d66] text-xs font-medium tracking-wide;
}

/* Эффекты свечения для интерактивных элементов */
.battle-glow-gold {
    box-shadow: 0 0 8px rgba(193, 169, 110, 0.4);
}

.battle-glow-gold:hover {
    box-shadow: 0 0 12px rgba(193, 169, 110, 0.6);
}

/* Стили для заблокированных элементов */
.battle-blocked-overlay {
    @apply absolute inset-0 bg-gradient-to-r from-transparent via-black/10 to-transparent pointer-events-none;
}

.battle-blocked-pattern {
    @apply absolute inset-0 opacity-5 pointer-events-none;
    background-image: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 10px,
        rgba(193, 169, 110, 0.3) 10px,
        rgba(193, 169, 110, 0.3) 20px
    );
}

/* Стили для индикаторов статуса */
.battle-status-indicator {
    @apply w-2 h-2 rounded-full animate-pulse;
}

.battle-status-development {
    @apply bg-gradient-to-r from-[#c1a96e] to-[#998d66];
    box-shadow: 0 0 4px rgba(193, 169, 110, 0.4);
}

.battle-status-maintenance {
    @apply bg-gradient-to-r from-[#6e3f35] to-[#3c221b];
    box-shadow: 0 0 4px rgba(110, 63, 53, 0.4);
}

/* Адаптивные стили */
@media (min-width: 768px) {
    .battle-location-item {
        @apply cursor-default;
    }
    
    .battle-location-item-inactive {
        @apply cursor-pointer;
    }
}

/* Стили для темной фантазийной атмосферы */
.battle-dark-fantasy {
    background: linear-gradient(135deg, #1a1814 0%, #2f2d2b 100%);
}

.battle-text-shadow {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

.battle-drop-shadow {
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.8));
}

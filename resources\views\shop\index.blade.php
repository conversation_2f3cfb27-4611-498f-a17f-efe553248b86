<!DOCTYPE html>
<html lang="en">
@php use Illuminate\Support\Facades\Auth; @endphp {{-- Используем фасад Auth --}}

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    {{-- Устанавливаем заголовок страницы с проверкой на авторизацию --}}
    <title>Магазин - Вещи | {{ Auth::check() ? Auth::user()->name : ($user->name ?? 'Гость') }}</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    {{-- Подключаем Vite для CSS и JS --}}
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>

{{-- Устанавливаем фон и основной шрифт для тела страницы --}}

<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif min-h-screen flex flex-col">
    {{-- Основной контейнер страницы --}}
    <div
        class="flex-grow container max-w-md mx-auto px-1 py-0 bg-[#2f2d2b] border-2 border-[#a6925e] rounded-lg shadow-lg overflow-hidden">

        {{-- Сообщение приветствия --}}
        @if (session('welcome_message'))
            <div class="bg-[#3b3a33] text-white p-4 rounded mb-4 text-center">
                {{ session('welcome_message') }}
            </div>
        @endif

        {{-- HP/MP блок с уведомлениями --}}
        <x-layout.hp-mp-bar :actualResources="$actualResources" :userProfile="$userProfile">
            {{-- Слот для уведомлений между HP и MP --}}
            <x-layout.notifications-bar :hasUnreadMessages="$hasUnreadMessages ?? false"
                :unreadMessagesCount="$unreadMessagesCount ?? 0" :hasBrokenItems="$hasBrokenItems ?? false"
                :brokenItemsCount="$brokenItemsCount ?? 0" />
        </x-layout.hp-mp-bar>

        {{-- Отображение валюты --}}
        <x-layout.currency-display :userProfile="$userProfile" :experienceProgress="$experienceProgress ?? null" />

        {{-- Блок изображения локации для страницы магазина --}}
        <x-layout.shop-location-image :breadcrumbs="$breadcrumbs" title="Магазин - Вещи"
            imagePath="assets/bannerShop.jpg" imageAlt="Баннер магазина" />

        {{-- Сообщения об успехе или ошибке --}}
        @if (session('success'))
            <div class="bg-green-500 text-white p-4 rounded mb-4 text-center mt-4">
                {{ session('success') }}
            </div>
        @endif

        @if (session('error'))
            <div class="bg-red-500 text-white p-4 rounded mb-4 text-center mt-4">
                {{ session('error') }}
            </div>
        @endif

        {{-- Кнопки переключения вкладок --}}
        <div class="flex flex-wrap justify-center gap-3 mt-2 mb-6">
            <a href="{{ route('shop.index') }}"
                class="relative overflow-hidden bg-gradient-to-b {{ request()->routeIs('shop.index') || request()->routeIs('shop.item.details') ? 'from-[#c4a76d] to-[#a6925e]' : 'from-[#514b3c] to-[#38352c]' }} py-2 px-4 rounded border {{ request()->routeIs('shop.index') || request()->routeIs('shop.item.details') ? 'border-[#d4b781]' : 'border-[#514b3c]' }} shadow-md transition-all duration-300 group hover:from-[#d4b781] hover:to-[#b7a36f] hover:border-[#d4b781]"
                style="min-width: 100px; text-align: center;">
                <span class="relative z-10 text-[#2f2d2b] font-medium">Вещи</span>
                <span
                    class="absolute inset-0 bg-gradient-to-r from-transparent via-[#f0d89e] to-transparent opacity-0 group-hover:opacity-20 transform translate-x-full group-hover:translate-x-0 transition-all duration-700"></span>
            </a>
            <a href="{{ route('shop.recipes') }}"
                class="relative overflow-hidden bg-gradient-to-b {{ request()->routeIs('shop.recipes') ? 'from-[#c4a76d] to-[#a6925e]' : 'from-[#514b3c] to-[#38352c]' }} py-2 px-4 rounded border {{ request()->routeIs('shop.recipes') ? 'border-[#d4b781]' : 'border-[#514b3c]' }} shadow-md transition-all duration-300 group hover:from-[#d4b781] hover:to-[#b7a36f] hover:border-[#d4b781]"
                style="min-width: 100px; text-align: center;">
                <span class="relative z-10 text-[#2f2d2b] font-medium">Рецепты</span>
                <span
                    class="absolute inset-0 bg-gradient-to-r from-transparent via-[#f0d89e] to-transparent opacity-0 group-hover:opacity-20 transform translate-x-full group-hover:translate-x-0 transition-all duration-700"></span>
            </a>
            {{-- Ресурсы (недоступно) --}}
            <div class="relative overflow-hidden bg-gradient-to-b from-[#6b6658] to-[#5a5449] py-2 px-4 rounded border border-[#6b6658] shadow-md cursor-not-allowed opacity-70"
                style="min-width: 100px; text-align: center;">
                <span class="relative z-10 text-[#a8a090] font-medium">Ресурсы</span>
                <span
                    class="absolute -top-1 -right-1 bg-[#a6925e] text-[#2f2d2b] text-xs px-1 py-0.5 rounded-md font-bold animate-pulse z-20">
                    В разработке
                </span>
            </div>
        </div>

        {{-- Фильтры (пока заглушка, можно расширить позже) --}}
        <div class="relative mt-4 mb-6">
            {{-- Декоративные элементы --}}
            <div class="absolute -top-1 left-1/2 transform -translate-x-1/2 w-32 h-1">
                <div class="w-full h-px bg-gradient-to-r from-transparent via-[#a6925e] to-transparent opacity-60">
                </div>
            </div>

            {{-- Заголовок фильтра --}}
            <h3 class="text-center text-[#e5b769] font-medium mb-3 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd"
                        d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z"
                        clip-rule="evenodd" />
                </svg>
                Фильтр товаров
            </h3>

            {{-- Основной блок фильтров --}}
            <div
                class="relative bg-gradient-to-b from-[#312e25] to-[#1d1b16] rounded-lg p-4 border border-[#a6925e] shadow-lg overflow-hidden">
                {{-- Светящиеся края --}}
                <div
                    class="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-[#e5b769] to-transparent opacity-40">
                </div>
                <div
                    class="absolute inset-x-0 bottom-0 h-px bg-gradient-to-r from-transparent via-[#e5b769] to-transparent opacity-40">
                </div>
                <div
                    class="absolute inset-y-0 left-0 w-px bg-gradient-to-b from-transparent via-[#e5b769] to-transparent opacity-40">
                </div>
                <div
                    class="absolute inset-y-0 right-0 w-px bg-gradient-to-b from-transparent via-[#e5b769] to-transparent opacity-40">
                </div>

                {{-- Форма фильтра --}}
                <form id="filterForm" method="GET" action="{{ route('shop.index') }}" class="relative z-10">
                    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2">
                        {{-- Тип предмета --}}
                        <div class="group">
                            <label for="itemType"
                                class="block text-sm font-medium text-[#e5b769] mb-1.5 transition-colors duration-300 group-hover:text-[#f0d89e]">
                                Тип предмета
                            </label>
                            <div class="relative">
                                <select id="itemType" name="type"
                                    class="w-full bg-[#252117] text-[#d9d3b8] py-2 px-3 pr-8 rounded border border-[#514b3c] focus:border-[#a6925e] focus:ring focus:ring-[#a6925e] focus:ring-opacity-30 appearance-none transition-all duration-300 text-sm">
                                    <option value="">Все</option>
                                    @foreach($filterData['types'] ?? [] as $type)
                                        <option value="{{ $type }}" {{ request('type') == $type ? 'selected' : '' }}>
                                            {{ $type }}
                                        </option>
                                    @endforeach
                                </select>
                                {{-- Иконка стрелки --}}
                                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-[#a6925e]" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 9l-7 7-7-7" />
                                    </svg>
                                </div>
                            </div>
                        </div>

                        {{-- Качество --}}
                        <div class="group">
                            <label for="quality"
                                class="block text-sm font-medium text-[#e5b769] mb-1.5 transition-colors duration-300 group-hover:text-[#f0d89e]">
                                Качество
                            </label>
                            <div class="relative">
                                <select id="quality" name="quality"
                                    class="w-full bg-[#252117] text-[#d9d3b8] py-2 px-3 pr-8 rounded border border-[#514b3c] focus:border-[#a6925e] focus:ring focus:ring-[#a6925e] focus:ring-opacity-30 appearance-none transition-all duration-300 text-sm">
                                    <option value="">Все</option>
                                    @foreach($filterData['qualities'] ?? [] as $quality)
                                                                    <option value="{{ $quality }}" {{ request('quality') == $quality ? 'selected' : '' }}
                                                                        class="{{ $quality == 'Обычное' ? 'text-gray-200' :
                                        ($quality == 'Необычное' ? 'text-green-400' :
                                            ($quality == 'Редкое' ? 'text-blue-400' :
                                                ($quality == 'Эпическое' ? 'text-purple-400' :
                                                    ($quality == 'Легендарное' ? 'text-orange-400' : '')))) }}">
                                                                        {{ $quality }}
                                                                    </option>
                                    @endforeach
                                </select>
                                {{-- Иконка стрелки --}}
                                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-[#a6925e]" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 9l-7 7-7-7" />
                                    </svg>
                                </div>
                            </div>
                        </div>

                        {{-- Атрибут --}}
                        <div class="group">
                            <label for="attribute"
                                class="block text-sm font-medium text-[#e5b769] mb-1.5 transition-colors duration-300 group-hover:text-[#f0d89e]">
                                Основной атрибут
                            </label>
                            <div class="relative">
                                <select id="attribute" name="attribute"
                                    class="w-full bg-[#252117] text-[#d9d3b8] py-2 px-3 pr-8 rounded border border-[#514b3c] focus:border-[#a6925e] focus:ring focus:ring-[#a6925e] focus:ring-opacity-30 appearance-none transition-all duration-300 text-sm">
                                    <option value="">Все атрибуты</option>
                                    <option value="strength" {{ request('attribute') == 'strength' ? 'selected' : '' }}>
                                        Сила</option>
                                    <option value="intelligence" {{ request('attribute') == 'intelligence' ? 'selected' : '' }}>Интеллект</option>
                                    <option value="armor" {{ request('attribute') == 'armor' ? 'selected' : '' }}>Броня
                                    </option>
                                    <option value="hp" {{ request('attribute') == 'hp' ? 'selected' : '' }}>Здоровье
                                    </option>
                                    <option value="mp" {{ request('attribute') == 'mp' ? 'selected' : '' }}>Мана</option>
                                    <option value="recovery" {{ request('attribute') == 'recovery' ? 'selected' : '' }}>
                                        Восстановление</option>
                                </select>
                                {{-- Иконка стрелки --}}
                                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-[#a6925e]" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 9l-7 7-7-7" />
                                    </svg>
                                </div>
                            </div>
                        </div>

                        {{-- Цена (Валюта) --}}
                        {{-- Этот блок отвечает за фильтрацию товаров по валюте.
                        Важно: name должен быть price_type, чтобы фильтр работал корректно в контроллере.
                        При выборе валюты будут отображаться только товары, у которых цена указана только в выбранной
                        валюте, а в остальных валютах — 0. --}}
                        <div class="group">
                            <label for="price"
                                class="block text-sm font-medium text-[#e5b769] mb-1.5 transition-colors duration-300 group-hover:text-[#f0d89e]">
                                Валюта
                            </label>
                            <div class="relative">
                                <select id="price" name="price_type"
                                    class="w-full bg-[#252117] text-[#d9d3b8] py-2 px-3 pr-8 rounded border border-[#514b3c] focus:border-[#a6925e] focus:ring focus:ring-[#a6925e] focus:ring-opacity-30 appearance-none transition-all duration-300 text-sm">
                                    <option value="">Любая валюта</option>
                                    <option value="bronze" {{ request('price_type') == 'bronze' ? 'selected' : '' }}
                                        class="text-[#cd7f32]">Бронза</option>
                                    <option value="silver" {{ request('price_type') == 'silver' ? 'selected' : '' }}
                                        class="text-[#c0c0c0]">Серебро</option>
                                    <option value="gold" {{ request('price_type') == 'gold' ? 'selected' : '' }}
                                        class="text-[#e5b769]">Золото</option>
                                </select>
                                {{-- Иконка стрелки --}}
                                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-[#a6925e]" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 9l-7 7-7-7" />
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>

                    {{-- Кнопки действий --}}
                    <div class="flex justify-center space-x-4 mt-5">
                        {{-- Применить фильтр --}}
                        <button type="submit"
                            class="relative overflow-hidden bg-gradient-to-b from-[#a6925e] to-[#8b7c4b] text-[#2f2d2b] py-2 px-6 rounded font-medium border border-[#c4a76d] hover:from-[#c4a76d] hover:to-[#a6925e] transition-all duration-300 shadow-md group">
                            <span class="relative z-10 flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20"
                                    fill="currentColor">
                                    <path fill-rule="evenodd"
                                        d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z"
                                        clip-rule="evenodd" />
                                </svg>
                                <span>Применить</span>
                            </span>
                            <span
                                class="absolute inset-0 bg-gradient-to-r from-transparent via-[#f0d89e] to-transparent opacity-0 group-hover:opacity-20 transform translate-x-full group-hover:translate-x-0 transition-all duration-700"></span>
                        </button>

                        {{-- Сбросить фильтр --}}
                        <a href="{{ route('shop.index') }}"
                            class="relative overflow-hidden bg-gradient-to-b from-[#38352c] to-[#292722] text-[#d9d3b8] py-2 px-6 rounded font-medium border border-[#514b3c] hover:from-[#45413a] hover:to-[#33312c] hover:text-[#e5b769] transition-all duration-300 shadow-md group">
                            <span class="relative z-10 flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20"
                                    fill="currentColor">
                                    <path fill-rule="evenodd"
                                        d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z"
                                        clip-rule="evenodd" />
                                </svg>
                                <span>Сбросить</span>
                            </span>
                            <span
                                class="absolute inset-0 bg-gradient-to-r from-transparent via-[#a6925e] to-transparent opacity-0 group-hover:opacity-20 transform translate-x-full group-hover:translate-x-0 transition-all duration-700"></span>
                        </a>
                    </div>
                </form>
            </div>

            {{-- Декоративные элементы внизу --}}
            <div class="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-32 h-1">
                <div class="w-full h-px bg-gradient-to-r from-transparent via-[#a6925e] to-transparent opacity-60">
                </div>
            </div>
        </div>

        {{-- Секция с товарами --}}
        <div class="mt-6">
            <h1 class="text-3xl font-bold text-[#e5b769] mb-6 text-center">Список товаров</h1>

            @if($shopItems->isEmpty())
                <div class="bg-[#3b3a33] border border-[#a6925e] rounded-lg p-8 shadow-lg text-center">
                    <p class="text-[#d9d3b8]">Товары не найдены. Попробуйте изменить параметры фильтра.</p>
                </div>
            @else
                {{-- Сетка товаров --}}
                <div class="grid grid-cols-1 gap-6 max-w-md mx-auto">
                    @foreach ($shopItems as $shopItem)
                        @php
                            $item = $shopItem->item;
                            $itemGs = $shopItem->calculated_gs ?? 0;
                            $qualityColors = [
                                'Обычное' => 'text-gray-200',
                                'Необычное' => 'text-green-400',
                                'Редкое' => 'text-blue-400',
                                'Эпическое' => 'text-purple-400',
                                'Легендарное' => 'text-orange-400',
                            ];
                            $qualityColor = $qualityColors[$item->quality] ?? 'text-gray-200';

                            // Определение класса для эффекта свечения в зависимости от качества
                            $qualityGlowMap = [
                                'Обычное' => '', // Обычные предметы не светятся
                                'Необычное' => 'animate-breathing-glow glow-color-green-400',
                                'Редкое' => 'animate-breathing-glow glow-color-blue-400',
                                'Эпическое' => 'animate-breathing-glow glow-color-purple-400',
                                'Легендарное' => 'animate-breathing-glow glow-color-orange-400',
                            ];
                            $qualityGlow = $qualityGlowMap[$item->quality] ?? '';
                            $maxUpgradeLevel = $item->max_upgrade_level ?? 5; // Максимальный уровень улучшения
                            // Ищем экипированный предмет для сравнения
                            // Сначала проверяем, есть ли предмет в соответствующем слоте
                            $equipped = $equippedItems->get($item->type);

                            // Если в слоте нет предмета, проверяем, может быть точно такой же предмет надет в другом слоте
                            if (!$equipped) {
                                $equipped = $equippedItemsCollection->first(function ($equippedItem) use ($item) {
                                    return $equippedItem->item_id === $item->id;
                                });
                            }

                            // Рассчитываем разницу для каждого атрибута
                            $attributeDiffs = [];
                            // Список всех атрибутов предмета, которые будем сравнивать
                            $attributes = ['strength', 'intelligence', 'recovery', 'armor', 'crit_chance', 'crit_damage', 'hp', 'mp', 'resistance_fire', 'resistance_lightning'];
                            $totalDiff = 0;

                            // Проходим по всем атрибутам и вычисляем разницу между предметом в магазине и экипированным предметом
                            foreach ($attributes as $attr) {
                                // Получаем значение атрибута у предмета в магазине (Item)
                                $shopValue = $item->{$attr} ?? 0;

                                // Если экипированный предмет имеет тот же item_id, что и предмет в магазине,
                                // то это означает, что игрок уже купил и экипировал этот предмет.
                                // В таком случае сравниваем шаблон с самим собой (разница должна быть 0)
                                if ($equipped && $equipped->item_id === $item->id) {
                                    $equippedValue = $shopValue; // Используем значение из шаблона для корректного сравнения
                                } else {
                                    // Получаем значение атрибута у экипированного предмета (GameItem)
                                    $equippedValue = $equipped ? ($equipped->{$attr} ?? 0) : 0;
                                }

                                // Вычисляем разницу: положительная - предмет в магазине лучше, отрицательная - хуже
                                $attributeDiffs[$attr] = $shopValue - $equippedValue;
                                // Суммируем разницу для общего показателя
                                $totalDiff += $attributeDiffs[$attr];
                            }

                            // Используем общую разницу атрибутов вместо разницы GS
                            $gsDiff = $equipped ? $totalDiff : null;
                            // Цвет для разницы: зелёный если лучше, коричневый если хуже, серый если одинаково
                            $gsDiffColor = $gsDiff > 0 ? 'text-green-400' : ($gsDiff < 0 ? 'text-[#cd7f32]' : ($gsDiff === 0 ? 'text-gray-400' : ''));
                        @endphp
                        {{-- Карточка товара --}}
                        <div
                            class="relative rounded-lg overflow-hidden shadow-lg transform transition-all duration-300 hover:scale-[1.02]">
                            {{-- Фоновый градиент основан на качестве предмета --}}
                            <div class="absolute inset-0 bg-gradient-to-br from-[#2a2621] to-[#1a1814] opacity-95"></div>

                            {{-- Декоративная рамка --}}
                            <div class="absolute inset-0 rounded-lg border-2 border-[#a6925e] pointer-events-none"></div>
                            <div
                                class="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-[#e5b769] to-transparent opacity-60">
                            </div>
                            <div
                                class="absolute inset-x-0 bottom-0 h-px bg-gradient-to-r from-transparent via-[#e5b769] to-transparent opacity-60">
                            </div>

                            {{-- Содержимое карточки --}}
                            <div class="relative p-4">
                                {{-- Заголовок с иконкой и информацией --}}
                                <div class="flex items-start space-x-3 mb-3">
                                    {{-- Контейнер для иконки с градиентным фоном и свечением --}}
                                    <div class="relative">
                                        <div
                                            class="absolute inset-0 rounded-md bg-gradient-to-br from-[#3b3a33] to-[#252117] blur-[1px]">
                                        </div>
                                        <div
                                            class="relative w-16 h-16 bg-[#252117] rounded-md overflow-hidden flex items-center justify-center shadow-md">
                                            <img src="{{ asset($item->icon ?? 'assets/placeholder.png') }}"
                                                alt="{{ $item->name }}" class="w-14 h-14 object-contain {{ $qualityGlow }}"
                                                style="image-rendering: crisp-edges;">
                                        </div>
                                        <div
                                            class="absolute -bottom-1 -right-1 px-1.5 py-0.5 bg-[#2a2721] text-xs font-bold rounded border border-[#a6925e] flex items-center gap-1">
                                            @if($gsDiff !== null && $gsDiff > 0)
                                                {{-- Если предмет на витрине лучше, показываем прирост зелёным --}}
                                                <span class="text-green-400">+{{ $gsDiff }}</span>
                                            @elseif($gsDiff !== null && $gsDiff < 0)
                                                {{-- Если предмет на витрине хуже, показываем отрицательную разницу коричневым --}}
                                                <span class="text-[#cd7f32]">{{ $gsDiff }}</span>
                                            @elseif($gsDiff !== null && $gsDiff === 0)
                                                {{-- Если характеристики одинаковые, показываем 0 серым цветом --}}
                                                <span class="text-gray-400">0</span>
                                            @else
                                                {{-- Если нет экипированного предмета, показываем только GS предмета --}}
                                                <span class="text-green-400">+{{ $itemGs }}</span>
                                            @endif
                                        </div>
                                    </div>

                                    {{-- Информация о предмете --}}
                                    <div class="flex-1">
                                        <h3 class="text-base font-bold {{ $qualityColor }} leading-tight mb-1"
                                            style="text-shadow: 0px 1px 3px rgba(0,0,0,0.8);">
                                            {{ $item->name }}
                                        </h3>

                                        {{-- Тип и уровень --}}
                                        <div class="flex items-center text-xs mb-1.5 text-[#d9d3b8]">
                                            <span class="px-1.5 py-0.5 bg-[#38352c] rounded border border-[#514b3c] mr-1.5">
                                                {{ $item->type }}
                                            </span>
                                            <span class="px-1.5 py-0.5 bg-[#38352c] rounded border border-[#514b3c]">
                                                Ур: {{ $item->level_required }}
                                            </span>
                                        </div>

                                        {{-- Доступные улучшения и прочность --}}
                                        <div class="flex justify-between">
                                            <div class="text-xs text-[#e5b769]">
                                                <i class="fas fa-arrow-up text-[10px] mr-0.5"></i>
                                                Улучшений: <span class="font-medium">0/{{ $maxUpgradeLevel }}</span>
                                            </div>
                                            <div class="text-xs text-[#e5b769]">
                                                <i class="fas fa-shield-alt text-[10px] mr-0.5"></i>
                                                Прочность: <span class="font-medium">{{ $item->base_max_durability }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {{-- Цена предмета --}}
                                <div class="flex items-center justify-between mb-3">
                                    <div
                                        class="flex items-center space-x-2 px-2 py-1 bg-[#2a2621] rounded border border-[#514b3c]">
                                        <span class="text-xs text-[#9a9483]">Цена:</span>
                                        <div class="flex items-center space-x-1">
                                            @if ($shopItem->price_gold > 0)
                                                <div class="flex items-center">
                                                    <img src="{{ asset('assets/goldIcon.png') }}" alt="Золото"
                                                        class="w-4 h-4 mr-0.5">
                                                    <span
                                                        class="text-xs font-medium text-[#e5b769]">{{ $shopItem->price_gold }}</span>
                                                </div>
                                            @endif

                                            @if ($shopItem->price_silver > 0)
                                                <div class="flex items-center">
                                                    <img src="{{ asset('assets/silverIcon.png') }}" alt="Серебро"
                                                        class="w-4 h-4 mr-0.5">
                                                    <span
                                                        class="text-xs font-medium text-[#c0c0c0]">{{ $shopItem->price_silver }}</span>
                                                </div>
                                            @endif

                                            @if ($shopItem->price_bronze > 0)
                                                <div class="flex items-center">
                                                    <img src="{{ asset('assets/bronzeIcon.png') }}" alt="Бронза"
                                                        class="w-4 h-4 mr-0.5">
                                                    <span
                                                        class="text-xs font-medium text-[#cd7f32]">{{ $shopItem->price_bronze }}</span>
                                                </div>
                                            @endif
                                        </div>
                                    </div>

                                    {{-- Качество предмета с цветовой меткой --}}
                                    <div class="flex items-center">
                                        <div class="w-2 h-2 rounded-full mr-1 {{ str_replace('text', 'bg', $qualityColor) }}">
                                        </div>
                                        <span class="text-[11px] {{ $qualityColor }}">{{ $item->quality }}</span>
                                    </div>
                                </div>

                                {{-- Кнопки действий --}}
                                <div class="grid grid-cols-2 gap-2">
                                    <form action="{{ route('shop.buy', $shopItem) }}" method="POST">
                                        @csrf
                                        <button type="submit"
                                            class="w-full bg-gradient-to-b from-[#c4a76d] to-[#a6925e] text-[#2f2d2b] py-2 px-3 text-sm font-medium rounded shadow-md hover:from-[#d4b781] hover:to-[#b7a36f] transition-all duration-300 border border-[#d4b781] flex items-center justify-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20"
                                                fill="currentColor">
                                                <path
                                                    d="M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3z">
                                                </path>
                                            </svg>
                                            Купить
                                        </button>
                                    </form>
                                    {{-- Кнопка для открытия модального окна с подробностями предмета --}}
                                    <button type="button"
                                        class="w-full bg-gradient-to-b from-[#514b3c] to-[#38352c] text-[#d9d3b8] py-2 px-3 text-sm font-medium rounded shadow-md hover:from-[#5d5745] hover:to-[#433f35] transition-all duration-300 border border-[#514b3c] flex items-center justify-center"
                                        onclick="showItemDetailsModal({{ $shopItem->id }})">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20"
                                            fill="currentColor">
                                            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                                            <path fill-rule="evenodd"
                                                d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                                                clip-rule="evenodd"></path>
                                        </svg>
                                        Подробнее
                                    </button>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            @endif
        </div>

        {{-- Секция с пагинацией (заменить текущую) --}}
        {{-- Пагинация в стиле проекта - полностью выровненный дизайн --}}
        @if ($paginatedItems->hasPages())
            <div class="mt-6 mb-4 flex justify-center items-center">
                {{-- Более симметричный контейнер с выпуклым эффектом --}}
                <div class="relative">
                    {{-- Основной контейнер пагинации с эффектом выпуклости --}}
                    <div
                        class="relative bg-gradient-to-b from-[#312e25] to-[#1a1814] rounded-lg px-5 py-3
                                                                                                                                                                                                                                        border border-[#a6925e] shadow-md transform-gpu
                                                                                                                                                                                                                                        before:absolute before:inset-0 before:bg-gradient-to-t before:from-transparent before:to-[rgba(229,183,105,0.10)] before:rounded-lg
                                                                                                                                                                                                                                        before:pointer-events-none before:opacity-80
                                                                                                                                                                                                                                        after:absolute after:inset-0 after:rounded-lg after:shadow-[inset_0_1px_2px_rgba(255,255,255,0.15),0_3px_8px_rgba(0,0,0,0.4)]
                                                                                                                                                                                                                                        after:pointer-events-none">

                        {{-- Декоративные линии вверху и внизу --}}
                        <div
                            class="absolute top-[6px] left-3 right-3 h-[1px] bg-gradient-to-r from-transparent via-[#e5b769] to-transparent opacity-60 pointer-events-none">
                        </div>
                        <div
                            class="absolute bottom-[6px] left-3 right-3 h-[1px] bg-gradient-to-r from-transparent via-[#e5b769] to-transparent opacity-60 pointer-events-none">
                        </div>

                        <div class="flex items-center justify-center">
                            {{-- Предыдущая страница --}}
                            @if ($paginatedItems->onFirstPage())
                                <button disabled
                                    class="w-10 h-10 flex items-center justify-center rounded border border-[#3d3a32] bg-[#1a1814] text-[#514b3c] cursor-not-allowed opacity-70 mr-2 pointer-events-none">
                                    <span class="text-lg font-medium">←</span>
                                </button>
                            @else
                                <a href="{{ $paginatedItems->previousPageUrl() }}"
                                    class="w-10 h-10 flex items-center justify-center rounded border border-[#514b3c] bg-[#252117] hover:bg-[#2d2820] hover:border-[#a6925e] transition-all duration-300 text-[#a6925e] mr-2 hover:text-[#e5b769] pointer-events-auto">
                                    <span class="text-lg font-medium">←</span>
                                </a>
                            @endif

                            {{-- Контейнер для номеров страниц (центрированный) --}}
                            <div class="flex items-center justify-center">
                                @foreach ($paginatedItems->getUrlRange(1, $paginatedItems->lastPage()) as $page => $url)
                                                    <a href="{{ $url }}"
                                                        class="relative w-10 h-10 flex items-center justify-center mx-1 rounded {{ $page == $paginatedItems->currentPage()
                                    ? 'border border-[#e5b769] bg-[#2d2820] text-[#e5b769] font-medium shadow-[inset_0_1px_3px_rgba(0,0,0,0.3)] pointer-events-auto'
                                    : 'border border-[#514b3c] bg-[#252117] text-[#a6925e] hover:bg-[#2d2820] hover:border-[#a6925e] hover:text-[#e5b769] pointer-events-auto' }}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            transition-colors duration-300">
                                                        @if ($page == $paginatedItems->currentPage())
                                                            {{-- Декоративные уголки только для активной страницы --}}
                                                            <div
                                                                class="absolute top-0 left-0 w-[5px] h-[5px] border-t border-l border-[#e5b769] opacity-80 pointer-events-none">
                                                            </div>
                                                            <div
                                                                class="absolute top-0 right-0 w-[5px] h-[5px] border-t border-r border-[#e5b769] opacity-80 pointer-events-none">
                                                            </div>
                                                            <div
                                                                class="absolute bottom-0 left-0 w-[5px] h-[5px] border-b border-l border-[#e5b769] opacity-80 pointer-events-none">
                                                            </div>
                                                            <div
                                                                class="absolute bottom-0 right-0 w-[5px] h-[5px] border-b border-r border-[#e5b769] opacity-80 pointer-events-none">
                                                            </div>
                                                            <span class="text-lg"
                                                                style="text-shadow: 0 0 4px rgba(229, 183, 105, 0.5);">{{ $page }}</span>
                                                        @else
                                                            <span class="text-lg">{{ $page }}</span>
                                                        @endif
                                                    </a>
                                @endforeach
                            </div>

                            {{-- Следующая страница --}}
                            @if ($paginatedItems->hasMorePages())
                                <a href="{{ $paginatedItems->nextPageUrl() }}"
                                    class="w-10 h-10 flex items-center justify-center rounded border border-[#514b3c] bg-[#252117] hover:bg-[#2d2820] hover:border-[#a6925e] transition-all duration-300 text-[#a6925e] ml-2 hover:text-[#e5b769] pointer-events-auto">
                                    <span class="text-lg font-medium">→</span>
                                </a>
                            @else
                                <button disabled
                                    class="w-10 h-10 flex items-center justify-center rounded border border-[#3d3a32] bg-[#1a1814] text-[#514b3c] cursor-not-allowed opacity-70 ml-2 pointer-events-none">
                                    <span class="text-lg font-medium">→</span>
                                </button>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            {{-- Информация о текущей странице - более четкая и лаконичная --}}
            <div class="text-center mb-4">
                <div class="inline-block px-3 py-1 bg-[#1a1814] rounded border-t border-b border-[#514b3c]">
                    <span class="text-[#9a9483] text-xs">
                        Страница <span class="text-[#e5b769]">{{ $paginatedItems->currentPage() }}</span> из <span
                            class="text-[#e5b769]">{{ $paginatedItems->lastPage() }}</span>
                        <span class="inline-block mx-1 text-[#514b3c]">•</span>
                        Всего: <span class="text-[#e5b769]">{{ $totalItemCount }}</span>
                    </span>
                </div>
            </div>
        @endif


    </div> {{-- Закрываем контейнер страницы --}}

    {{-- Нижние кнопки навигации --}}
    <div class="container mx-auto text-center px-2 py-2 flex justify-center space-x-2">
        {{-- Кнопка Рюкзак --}}
        <a href="{{ route('inventory.index') }}"
            class="bg-[#c4a76d] text-[#2f2d2b] py-1 px-1 rounded shadow-lg hover:bg-[#d4b781] transition duration-300">Рюкзак</a>
        {{-- Кнопка Персонаж --}}
        <a href="{{ route('user.profile') }}"
            class="bg-[#c4a76d] text-[#2f2d2b] py-1 px-1 rounded shadow-lg hover:bg-[#d4b781] transition duration-300">Персонаж</a>
        {{-- Кнопка Гильдия (ссылка-заглушка) --}}
        <a href="#"
            class="bg-[#c4a76d] text-[#2f2d2b] py-1 px-1 rounded shadow-lg hover:bg-[#d4b781] transition duration-300">Гильдия</a>
    </div>
    {{-- Футер --}}
    @if(Auth::check()) {{-- Показываем футер только авторизованным пользователям --}}
        {{-- Футер --}}
        <x-layout.footer :onlineCount="$onlineCount" />
    @endif

    {{-- Модальное окно для подробностей предмета (универсальное, скрыто по умолчанию) --}}
    <div id="itemDetailsModal"
        class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-60 hidden">
        <div
            class="relative bg-[#2f2d2b] rounded-lg shadow-lg max-w-lg w-full border-2 border-[#a6925e] overflow-y-auto max-h-[90vh]">

            {{-- Контейнер для AJAX-контента --}}
            <div id="itemDetailsModalContent" class="p-4">
                <div class="text-center text-[#e5b769]">Загрузка...</div>
            </div>
        </div>
    </div>

    {{-- JS для AJAX-подгрузки и управления модалкой --}}
    <script>
        // Открыть модалку и загрузить детали предмета по AJAX
        function showItemDetailsModal(shopItemId) {
            const modal = document.getElementById('itemDetailsModal');
            const content = document.getElementById('itemDetailsModalContent');
            modal.classList.remove('hidden');
            content.innerHTML = '<div class="text-center text-[#e5b769]">Загрузка...</div>';

            // AJAX-запрос к контроллеру для получения partial
            fetch('/shop/item-details/' + shopItemId)
                .then(response => {
                    // Проверяем, что ответ успешный
                    if (!response.ok) {
                        throw new Error('Ошибка сети: ' + response.status);
                    }
                    // Проверяем, что ответ в формате JSON
                    const contentType = response.headers.get('content-type');
                    if (!contentType || !contentType.includes('application/json')) {
                        throw new Error('Получен неверный формат данных');
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.html) {
                        content.innerHTML = data.html;
                    } else if (data.error) {
                        content.innerHTML = `<div class="text-center py-4">
                            <div class="text-red-500 text-lg mb-2">Произошла ошибка при загрузке информации о предмете.</div>
                            <div class="text-[#d9d3b8] text-sm">${data.error}</div>
                            <button onclick="closeItemDetailsModal()" class="mt-4 px-4 py-2 bg-[#38352c] text-[#e5b769] rounded border border-[#514b3c] hover:bg-[#4a452c]">
                                Закрыть
                            </button>
                        </div>`;
                    } else {
                        content.innerHTML = '<div class="text-red-500 text-center py-4">Произошла ошибка при загрузке информации о предмете.</div>';
                    }
                })
                .catch(error => {
                    console.error('Ошибка при загрузке информации о предмете:', error);
                    content.innerHTML = `<div class="text-center py-4">
                        <div class="text-red-500 text-lg mb-2">Произошла ошибка при загрузке информации о предмете.</div>
                        <div class="text-[#d9d3b8] text-sm">${error.message}</div>
                        <button onclick="closeItemDetailsModal()" class="mt-4 px-4 py-2 bg-[#38352c] text-[#e5b769] rounded border border-[#514b3c] hover:bg-[#4a452c]">
                            Закрыть
                        </button>
                    </div>`;
                });
        }

        // Закрыть модалку
        function closeItemDetailsModal() {
            document.getElementById('itemDetailsModal').classList.add('hidden');
        }

        // Закрытие по клику вне окна
        window.addEventListener('click', function (event) {
            const modal = document.getElementById('itemDetailsModal');
            if (event.target === modal) {
                closeItemDetailsModal();
            }
        });
    </script>
</body>

</html>
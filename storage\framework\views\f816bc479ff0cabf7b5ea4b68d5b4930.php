<!DOCTYPE html>
<html lang="en">
<?php use Illuminate\Support\Facades\Auth; ?> 

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <title>Магазин - Вещи | <?php echo e(Auth::check() ? Auth::user()->name : ($user->name ?? 'Гость')); ?></title>
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
</head>



<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif min-h-screen flex flex-col">
    
    <div
        class="flex-grow container max-w-md mx-auto px-1 py-0 bg-[#2f2d2b] border-2 border-[#a6925e] rounded-lg shadow-lg overflow-hidden">

        
        <?php if(session('welcome_message')): ?>
            <div class="bg-[#3b3a33] text-white p-4 rounded mb-4 text-center">
                <?php echo e(session('welcome_message')); ?>

            </div>
        <?php endif; ?>

        
        <?php if (isset($component)) { $__componentOriginalee64098a97531effaa5ca39da6b3f2bd = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee64098a97531effaa5ca39da6b3f2bd = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.hp-mp-bar','data' => ['actualResources' => $actualResources,'userProfile' => $userProfile]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.hp-mp-bar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['actualResources' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($actualResources),'userProfile' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($userProfile)]); ?>
            
            <?php if (isset($component)) { $__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.notifications-bar','data' => ['hasUnreadMessages' => $hasUnreadMessages ?? false,'unreadMessagesCount' => $unreadMessagesCount ?? 0,'hasBrokenItems' => $hasBrokenItems ?? false,'brokenItemsCount' => $brokenItemsCount ?? 0]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.notifications-bar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['hasUnreadMessages' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($hasUnreadMessages ?? false),'unreadMessagesCount' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($unreadMessagesCount ?? 0),'hasBrokenItems' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($hasBrokenItems ?? false),'brokenItemsCount' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($brokenItemsCount ?? 0)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb)): ?>
<?php $attributes = $__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb; ?>
<?php unset($__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb)): ?>
<?php $component = $__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb; ?>
<?php unset($__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb); ?>
<?php endif; ?>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee64098a97531effaa5ca39da6b3f2bd)): ?>
<?php $attributes = $__attributesOriginalee64098a97531effaa5ca39da6b3f2bd; ?>
<?php unset($__attributesOriginalee64098a97531effaa5ca39da6b3f2bd); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee64098a97531effaa5ca39da6b3f2bd)): ?>
<?php $component = $__componentOriginalee64098a97531effaa5ca39da6b3f2bd; ?>
<?php unset($__componentOriginalee64098a97531effaa5ca39da6b3f2bd); ?>
<?php endif; ?>

        
        <?php if (isset($component)) { $__componentOriginal85b5c6510a4667d62a225031f53f7a0d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal85b5c6510a4667d62a225031f53f7a0d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.currency-display','data' => ['userProfile' => $userProfile,'experienceProgress' => $experienceProgress ?? null]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.currency-display'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['userProfile' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($userProfile),'experienceProgress' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($experienceProgress ?? null)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal85b5c6510a4667d62a225031f53f7a0d)): ?>
<?php $attributes = $__attributesOriginal85b5c6510a4667d62a225031f53f7a0d; ?>
<?php unset($__attributesOriginal85b5c6510a4667d62a225031f53f7a0d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal85b5c6510a4667d62a225031f53f7a0d)): ?>
<?php $component = $__componentOriginal85b5c6510a4667d62a225031f53f7a0d; ?>
<?php unset($__componentOriginal85b5c6510a4667d62a225031f53f7a0d); ?>
<?php endif; ?>

        
        <?php if (isset($component)) { $__componentOriginald1aec9663525de1ed7a8ebcf8e6c5564 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald1aec9663525de1ed7a8ebcf8e6c5564 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.shop-location-image','data' => ['breadcrumbs' => $breadcrumbs,'title' => 'Магазин - Вещи','imagePath' => 'assets/bannerShop.jpg','imageAlt' => 'Баннер магазина']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.shop-location-image'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['breadcrumbs' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($breadcrumbs),'title' => 'Магазин - Вещи','imagePath' => 'assets/bannerShop.jpg','imageAlt' => 'Баннер магазина']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald1aec9663525de1ed7a8ebcf8e6c5564)): ?>
<?php $attributes = $__attributesOriginald1aec9663525de1ed7a8ebcf8e6c5564; ?>
<?php unset($__attributesOriginald1aec9663525de1ed7a8ebcf8e6c5564); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald1aec9663525de1ed7a8ebcf8e6c5564)): ?>
<?php $component = $__componentOriginald1aec9663525de1ed7a8ebcf8e6c5564; ?>
<?php unset($__componentOriginald1aec9663525de1ed7a8ebcf8e6c5564); ?>
<?php endif; ?>

        
        <?php if(session('success')): ?>
            <div class="bg-green-500 text-white p-4 rounded mb-4 text-center mt-4">
                <?php echo e(session('success')); ?>

            </div>
        <?php endif; ?>

        <?php if(session('error')): ?>
            <div class="bg-red-500 text-white p-4 rounded mb-4 text-center mt-4">
                <?php echo e(session('error')); ?>

            </div>
        <?php endif; ?>

        
        <div class="flex flex-wrap justify-center gap-3 mt-2 mb-6">
            <a href="<?php echo e(route('shop.index')); ?>"
                class="relative overflow-hidden bg-gradient-to-b <?php echo e(request()->routeIs('shop.index') || request()->routeIs('shop.item.details') ? 'from-[#c4a76d] to-[#a6925e]' : 'from-[#514b3c] to-[#38352c]'); ?> py-2 px-4 rounded border <?php echo e(request()->routeIs('shop.index') || request()->routeIs('shop.item.details') ? 'border-[#d4b781]' : 'border-[#514b3c]'); ?> shadow-md transition-all duration-300 group hover:from-[#d4b781] hover:to-[#b7a36f] hover:border-[#d4b781]"
                style="min-width: 100px; text-align: center;">
                <span class="relative z-10 text-[#2f2d2b] font-medium">Вещи</span>
                <span
                    class="absolute inset-0 bg-gradient-to-r from-transparent via-[#f0d89e] to-transparent opacity-0 group-hover:opacity-20 transform translate-x-full group-hover:translate-x-0 transition-all duration-700"></span>
            </a>
            <a href="<?php echo e(route('shop.recipes')); ?>"
                class="relative overflow-hidden bg-gradient-to-b <?php echo e(request()->routeIs('shop.recipes') ? 'from-[#c4a76d] to-[#a6925e]' : 'from-[#514b3c] to-[#38352c]'); ?> py-2 px-4 rounded border <?php echo e(request()->routeIs('shop.recipes') ? 'border-[#d4b781]' : 'border-[#514b3c]'); ?> shadow-md transition-all duration-300 group hover:from-[#d4b781] hover:to-[#b7a36f] hover:border-[#d4b781]"
                style="min-width: 100px; text-align: center;">
                <span class="relative z-10 text-[#2f2d2b] font-medium">Рецепты</span>
                <span
                    class="absolute inset-0 bg-gradient-to-r from-transparent via-[#f0d89e] to-transparent opacity-0 group-hover:opacity-20 transform translate-x-full group-hover:translate-x-0 transition-all duration-700"></span>
            </a>
            
            <div class="relative overflow-hidden bg-gradient-to-b from-[#6b6658] to-[#5a5449] py-2 px-4 rounded border border-[#6b6658] shadow-md cursor-not-allowed opacity-70"
                style="min-width: 100px; text-align: center;">
                <span class="relative z-10 text-[#a8a090] font-medium">Ресурсы</span>
                <span
                    class="absolute -top-1 -right-1 bg-[#a6925e] text-[#2f2d2b] text-xs px-1 py-0.5 rounded-md font-bold animate-pulse z-20">
                    В разработке
                </span>
            </div>
        </div>

        
        <div class="relative mt-4 mb-6">
            
            <div class="absolute -top-1 left-1/2 transform -translate-x-1/2 w-32 h-1">
                <div class="w-full h-px bg-gradient-to-r from-transparent via-[#a6925e] to-transparent opacity-60">
                </div>
            </div>

            
            <h3 class="text-center text-[#e5b769] font-medium mb-3 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd"
                        d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z"
                        clip-rule="evenodd" />
                </svg>
                Фильтр товаров
            </h3>

            
            <div
                class="relative bg-gradient-to-b from-[#312e25] to-[#1d1b16] rounded-lg p-4 border border-[#a6925e] shadow-lg overflow-hidden">
                
                <div
                    class="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-[#e5b769] to-transparent opacity-40">
                </div>
                <div
                    class="absolute inset-x-0 bottom-0 h-px bg-gradient-to-r from-transparent via-[#e5b769] to-transparent opacity-40">
                </div>
                <div
                    class="absolute inset-y-0 left-0 w-px bg-gradient-to-b from-transparent via-[#e5b769] to-transparent opacity-40">
                </div>
                <div
                    class="absolute inset-y-0 right-0 w-px bg-gradient-to-b from-transparent via-[#e5b769] to-transparent opacity-40">
                </div>

                
                <form id="filterForm" method="GET" action="<?php echo e(route('shop.index')); ?>" class="relative z-10">
                    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2">
                        
                        <div class="group">
                            <label for="itemType"
                                class="block text-sm font-medium text-[#e5b769] mb-1.5 transition-colors duration-300 group-hover:text-[#f0d89e]">
                                Тип предмета
                            </label>
                            <div class="relative">
                                <select id="itemType" name="type"
                                    class="w-full bg-[#252117] text-[#d9d3b8] py-2 px-3 pr-8 rounded border border-[#514b3c] focus:border-[#a6925e] focus:ring focus:ring-[#a6925e] focus:ring-opacity-30 appearance-none transition-all duration-300 text-sm">
                                    <option value="">Все</option>
                                    <?php $__currentLoopData = $filterData['types'] ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($type); ?>" <?php echo e(request('type') == $type ? 'selected' : ''); ?>>
                                            <?php echo e($type); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                
                                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-[#a6925e]" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 9l-7 7-7-7" />
                                    </svg>
                                </div>
                            </div>
                        </div>

                        
                        <div class="group">
                            <label for="quality"
                                class="block text-sm font-medium text-[#e5b769] mb-1.5 transition-colors duration-300 group-hover:text-[#f0d89e]">
                                Качество
                            </label>
                            <div class="relative">
                                <select id="quality" name="quality"
                                    class="w-full bg-[#252117] text-[#d9d3b8] py-2 px-3 pr-8 rounded border border-[#514b3c] focus:border-[#a6925e] focus:ring focus:ring-[#a6925e] focus:ring-opacity-30 appearance-none transition-all duration-300 text-sm">
                                    <option value="">Все</option>
                                    <?php $__currentLoopData = $filterData['qualities'] ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $quality): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                    <option value="<?php echo e($quality); ?>" <?php echo e(request('quality') == $quality ? 'selected' : ''); ?>

                                                                        class="<?php echo e($quality == 'Обычное' ? 'text-gray-200' :
                                        ($quality == 'Необычное' ? 'text-green-400' :
                                            ($quality == 'Редкое' ? 'text-blue-400' :
                                                ($quality == 'Эпическое' ? 'text-purple-400' :
                                                    ($quality == 'Легендарное' ? 'text-orange-400' : ''))))); ?>">
                                                                        <?php echo e($quality); ?>

                                                                    </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                
                                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-[#a6925e]" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 9l-7 7-7-7" />
                                    </svg>
                                </div>
                            </div>
                        </div>

                        
                        <div class="group">
                            <label for="attribute"
                                class="block text-sm font-medium text-[#e5b769] mb-1.5 transition-colors duration-300 group-hover:text-[#f0d89e]">
                                Основной атрибут
                            </label>
                            <div class="relative">
                                <select id="attribute" name="attribute"
                                    class="w-full bg-[#252117] text-[#d9d3b8] py-2 px-3 pr-8 rounded border border-[#514b3c] focus:border-[#a6925e] focus:ring focus:ring-[#a6925e] focus:ring-opacity-30 appearance-none transition-all duration-300 text-sm">
                                    <option value="">Все атрибуты</option>
                                    <option value="strength" <?php echo e(request('attribute') == 'strength' ? 'selected' : ''); ?>>
                                        Сила</option>
                                    <option value="intelligence" <?php echo e(request('attribute') == 'intelligence' ? 'selected' : ''); ?>>Интеллект</option>
                                    <option value="armor" <?php echo e(request('attribute') == 'armor' ? 'selected' : ''); ?>>Броня
                                    </option>
                                    <option value="hp" <?php echo e(request('attribute') == 'hp' ? 'selected' : ''); ?>>Здоровье
                                    </option>
                                    <option value="mp" <?php echo e(request('attribute') == 'mp' ? 'selected' : ''); ?>>Мана</option>
                                    <option value="recovery" <?php echo e(request('attribute') == 'recovery' ? 'selected' : ''); ?>>
                                        Восстановление</option>
                                </select>
                                
                                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-[#a6925e]" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 9l-7 7-7-7" />
                                    </svg>
                                </div>
                            </div>
                        </div>

                        
                        
                        <div class="group">
                            <label for="price"
                                class="block text-sm font-medium text-[#e5b769] mb-1.5 transition-colors duration-300 group-hover:text-[#f0d89e]">
                                Валюта
                            </label>
                            <div class="relative">
                                <select id="price" name="price_type"
                                    class="w-full bg-[#252117] text-[#d9d3b8] py-2 px-3 pr-8 rounded border border-[#514b3c] focus:border-[#a6925e] focus:ring focus:ring-[#a6925e] focus:ring-opacity-30 appearance-none transition-all duration-300 text-sm">
                                    <option value="">Любая валюта</option>
                                    <option value="bronze" <?php echo e(request('price_type') == 'bronze' ? 'selected' : ''); ?>

                                        class="text-[#cd7f32]">Бронза</option>
                                    <option value="silver" <?php echo e(request('price_type') == 'silver' ? 'selected' : ''); ?>

                                        class="text-[#c0c0c0]">Серебро</option>
                                    <option value="gold" <?php echo e(request('price_type') == 'gold' ? 'selected' : ''); ?>

                                        class="text-[#e5b769]">Золото</option>
                                </select>
                                
                                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-[#a6925e]" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 9l-7 7-7-7" />
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>

                    
                    <div class="flex justify-center space-x-4 mt-5">
                        
                        <button type="submit"
                            class="relative overflow-hidden bg-gradient-to-b from-[#a6925e] to-[#8b7c4b] text-[#2f2d2b] py-2 px-6 rounded font-medium border border-[#c4a76d] hover:from-[#c4a76d] hover:to-[#a6925e] transition-all duration-300 shadow-md group">
                            <span class="relative z-10 flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20"
                                    fill="currentColor">
                                    <path fill-rule="evenodd"
                                        d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z"
                                        clip-rule="evenodd" />
                                </svg>
                                <span>Применить</span>
                            </span>
                            <span
                                class="absolute inset-0 bg-gradient-to-r from-transparent via-[#f0d89e] to-transparent opacity-0 group-hover:opacity-20 transform translate-x-full group-hover:translate-x-0 transition-all duration-700"></span>
                        </button>

                        
                        <a href="<?php echo e(route('shop.index')); ?>"
                            class="relative overflow-hidden bg-gradient-to-b from-[#38352c] to-[#292722] text-[#d9d3b8] py-2 px-6 rounded font-medium border border-[#514b3c] hover:from-[#45413a] hover:to-[#33312c] hover:text-[#e5b769] transition-all duration-300 shadow-md group">
                            <span class="relative z-10 flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20"
                                    fill="currentColor">
                                    <path fill-rule="evenodd"
                                        d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z"
                                        clip-rule="evenodd" />
                                </svg>
                                <span>Сбросить</span>
                            </span>
                            <span
                                class="absolute inset-0 bg-gradient-to-r from-transparent via-[#a6925e] to-transparent opacity-0 group-hover:opacity-20 transform translate-x-full group-hover:translate-x-0 transition-all duration-700"></span>
                        </a>
                    </div>
                </form>
            </div>

            
            <div class="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-32 h-1">
                <div class="w-full h-px bg-gradient-to-r from-transparent via-[#a6925e] to-transparent opacity-60">
                </div>
            </div>
        </div>

        
        <div class="mt-6">
            <h1 class="text-3xl font-bold text-[#e5b769] mb-6 text-center">Список товаров</h1>

            <?php if($shopItems->isEmpty()): ?>
                <div class="bg-[#3b3a33] border border-[#a6925e] rounded-lg p-8 shadow-lg text-center">
                    <p class="text-[#d9d3b8]">Товары не найдены. Попробуйте изменить параметры фильтра.</p>
                </div>
            <?php else: ?>
                
                <div class="grid grid-cols-1 gap-6 max-w-md mx-auto">
                    <?php $__currentLoopData = $shopItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $shopItem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php
                            $item = $shopItem->item;
                            $itemGs = $shopItem->calculated_gs ?? 0;
                            $qualityColors = [
                                'Обычное' => 'text-gray-200',
                                'Необычное' => 'text-green-400',
                                'Редкое' => 'text-blue-400',
                                'Эпическое' => 'text-purple-400',
                                'Легендарное' => 'text-orange-400',
                            ];
                            $qualityColor = $qualityColors[$item->quality] ?? 'text-gray-200';

                            // Определение класса для эффекта свечения в зависимости от качества
                            $qualityGlowMap = [
                                'Обычное' => 'animate-breathing-glow glow-color-gray-200', // Обычные предметы светятся серым
                                'Необычное' => 'animate-breathing-glow glow-color-green-400',
                                'Редкое' => 'animate-breathing-glow glow-color-blue-400',
                                'Эпическое' => 'animate-breathing-glow glow-color-purple-400',
                                'Легендарное' => 'animate-breathing-glow glow-color-orange-400',
                            ];
                            $qualityGlow = $qualityGlowMap[$item->quality] ?? 'animate-breathing-glow glow-color-gray-200';
                            $maxUpgradeLevel = $item->max_upgrade_level ?? 5; // Максимальный уровень улучшения
                            // Ищем экипированный предмет для сравнения
                            // Сначала проверяем, есть ли предмет в соответствующем слоте
                            $equipped = $equippedItems->get($item->type);

                            // Если в слоте нет предмета, проверяем, может быть точно такой же предмет надет в другом слоте
                            if (!$equipped) {
                                $equipped = $equippedItemsCollection->first(function ($equippedItem) use ($item) {
                                    return $equippedItem->item_id === $item->id;
                                });
                            }

                            // Рассчитываем разницу для каждого атрибута
                            $attributeDiffs = [];
                            // Список всех атрибутов предмета, которые будем сравнивать
                            $attributes = ['strength', 'intelligence', 'recovery', 'armor', 'crit_chance', 'crit_damage', 'hp', 'mp', 'resistance_fire', 'resistance_lightning'];
                            $totalDiff = 0;

                            // Проходим по всем атрибутам и вычисляем разницу между предметом в магазине и экипированным предметом
                            foreach ($attributes as $attr) {
                                // Получаем значение атрибута у предмета в магазине (Item)
                                $shopValue = $item->{$attr} ?? 0;

                                // Если экипированный предмет имеет тот же item_id, что и предмет в магазине,
                                // то это означает, что игрок уже купил и экипировал этот предмет.
                                // В таком случае сравниваем шаблон с самим собой (разница должна быть 0)
                                if ($equipped && $equipped->item_id === $item->id) {
                                    $equippedValue = $shopValue; // Используем значение из шаблона для корректного сравнения
                                } else {
                                    // Получаем значение атрибута у экипированного предмета (GameItem)
                                    $equippedValue = $equipped ? ($equipped->{$attr} ?? 0) : 0;
                                }

                                // Вычисляем разницу: положительная - предмет в магазине лучше, отрицательная - хуже
                                $attributeDiffs[$attr] = $shopValue - $equippedValue;
                                // Суммируем разницу для общего показателя
                                $totalDiff += $attributeDiffs[$attr];
                            }

                            // Используем общую разницу атрибутов вместо разницы GS
                            $gsDiff = $equipped ? $totalDiff : null;
                            // Цвет для разницы: зелёный если лучше, коричневый если хуже, серый если одинаково
                            $gsDiffColor = $gsDiff > 0 ? 'text-green-400' : ($gsDiff < 0 ? 'text-[#cd7f32]' : ($gsDiff === 0 ? 'text-gray-400' : ''));
                        ?>
                        
                        <div
                            class="relative rounded-lg overflow-hidden shadow-lg transform transition-all duration-300 hover:scale-[1.02]">
                            
                            <div class="absolute inset-0 bg-gradient-to-br from-[#2a2621] to-[#1a1814] opacity-95"></div>

                            
                            <div class="absolute inset-0 rounded-lg border-2 border-[#a6925e] pointer-events-none"></div>
                            <div
                                class="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-[#e5b769] to-transparent opacity-60">
                            </div>
                            <div
                                class="absolute inset-x-0 bottom-0 h-px bg-gradient-to-r from-transparent via-[#e5b769] to-transparent opacity-60">
                            </div>

                            
                            <div class="relative p-4">
                                
                                <div class="flex items-start space-x-3 mb-3">
                                    
                                    <div class="relative">
                                        <div class="relative w-16 h-16 flex items-center justify-center">
                                            <img src="<?php echo e(asset($item->icon ?? 'assets/placeholder.png')); ?>"
                                                alt="<?php echo e($item->name); ?>" class="w-14 h-14 object-contain <?php echo e($qualityGlow); ?>"
                                                style="image-rendering: crisp-edges;">
                                        </div>
                                        <div
                                            class="absolute -bottom-1 -right-1 px-1.5 py-0.5 bg-[#2a2721] text-xs font-bold rounded border border-[#a6925e] flex items-center gap-1 z-10">
                                            <?php if($gsDiff !== null && $gsDiff > 0): ?>
                                                
                                                <span class="text-green-400">+<?php echo e($gsDiff); ?></span>
                                            <?php elseif($gsDiff !== null && $gsDiff < 0): ?>
                                                
                                                <span class="text-[#cd7f32]"><?php echo e($gsDiff); ?></span>
                                            <?php elseif($gsDiff !== null && $gsDiff === 0): ?>
                                                
                                                <span class="text-gray-400">0</span>
                                            <?php else: ?>
                                                
                                                <span class="text-green-400">+<?php echo e($itemGs); ?></span>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    
                                    <div class="flex-1">
                                        <h3 class="text-base font-bold <?php echo e($qualityColor); ?> leading-tight mb-1"
                                            style="text-shadow: 0px 1px 3px rgba(0,0,0,0.8);">
                                            <?php echo e($item->name); ?>

                                        </h3>

                                        
                                        <div class="flex items-center text-xs mb-1.5 text-[#d9d3b8]">
                                            <span class="px-1.5 py-0.5 bg-[#38352c] rounded border border-[#514b3c] mr-1.5">
                                                <?php echo e($item->type); ?>

                                            </span>
                                            <span class="px-1.5 py-0.5 bg-[#38352c] rounded border border-[#514b3c]">
                                                Ур: <?php echo e($item->level_required); ?>

                                            </span>
                                        </div>

                                        
                                        <div class="flex justify-between">
                                            <div class="text-xs text-[#e5b769]">
                                                <i class="fas fa-arrow-up text-[10px] mr-0.5"></i>
                                                Улучшений: <span class="font-medium">0/<?php echo e($maxUpgradeLevel); ?></span>
                                            </div>
                                            <div class="text-xs text-[#e5b769]">
                                                <i class="fas fa-shield-alt text-[10px] mr-0.5"></i>
                                                Прочность: <span class="font-medium"><?php echo e($item->base_max_durability); ?></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                
                                <div class="flex items-center justify-between mb-3">
                                    <div
                                        class="flex items-center space-x-2 px-2 py-1 bg-[#2a2621] rounded border border-[#514b3c]">
                                        <span class="text-xs text-[#9a9483]">Цена:</span>
                                        <div class="flex items-center space-x-1">
                                            <?php if($shopItem->price_gold > 0): ?>
                                                <div class="flex items-center">
                                                    <img src="<?php echo e(asset('assets/goldIcon.png')); ?>" alt="Золото"
                                                        class="w-4 h-4 mr-0.5">
                                                    <span
                                                        class="text-xs font-medium text-[#e5b769]"><?php echo e($shopItem->price_gold); ?></span>
                                                </div>
                                            <?php endif; ?>

                                            <?php if($shopItem->price_silver > 0): ?>
                                                <div class="flex items-center">
                                                    <img src="<?php echo e(asset('assets/silverIcon.png')); ?>" alt="Серебро"
                                                        class="w-4 h-4 mr-0.5">
                                                    <span
                                                        class="text-xs font-medium text-[#c0c0c0]"><?php echo e($shopItem->price_silver); ?></span>
                                                </div>
                                            <?php endif; ?>

                                            <?php if($shopItem->price_bronze > 0): ?>
                                                <div class="flex items-center">
                                                    <img src="<?php echo e(asset('assets/bronzeIcon.png')); ?>" alt="Бронза"
                                                        class="w-4 h-4 mr-0.5">
                                                    <span
                                                        class="text-xs font-medium text-[#cd7f32]"><?php echo e($shopItem->price_bronze); ?></span>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    
                                    <div class="flex items-center">
                                        <div class="w-2 h-2 rounded-full mr-1 <?php echo e(str_replace('text', 'bg', $qualityColor)); ?>">
                                        </div>
                                        <span class="text-[11px] <?php echo e($qualityColor); ?>"><?php echo e($item->quality); ?></span>
                                    </div>
                                </div>

                                
                                <div class="grid grid-cols-2 gap-2">
                                    <form action="<?php echo e(route('shop.buy', $shopItem)); ?>" method="POST">
                                        <?php echo csrf_field(); ?>
                                        <button type="submit"
                                            class="w-full bg-gradient-to-b from-[#c4a76d] to-[#a6925e] text-[#2f2d2b] py-2 px-3 text-sm font-medium rounded shadow-md hover:from-[#d4b781] hover:to-[#b7a36f] transition-all duration-300 border border-[#d4b781] flex items-center justify-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20"
                                                fill="currentColor">
                                                <path
                                                    d="M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3z">
                                                </path>
                                            </svg>
                                            Купить
                                        </button>
                                    </form>
                                    
                                    <button type="button"
                                        class="w-full bg-gradient-to-b from-[#514b3c] to-[#38352c] text-[#d9d3b8] py-2 px-3 text-sm font-medium rounded shadow-md hover:from-[#5d5745] hover:to-[#433f35] transition-all duration-300 border border-[#514b3c] flex items-center justify-center"
                                        onclick="showItemDetailsModal(<?php echo e($shopItem->id); ?>)">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20"
                                            fill="currentColor">
                                            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                                            <path fill-rule="evenodd"
                                                d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                                                clip-rule="evenodd"></path>
                                        </svg>
                                        Подробнее
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            <?php endif; ?>
        </div>

        
        
        <?php if($paginatedItems->hasPages()): ?>
            <div class="mt-6 mb-4 flex justify-center items-center">
                
                <div class="relative">
                    
                    <div
                        class="relative bg-gradient-to-b from-[#312e25] to-[#1a1814] rounded-lg px-5 py-3
                                                                                                                                                                                                                                                border border-[#a6925e] shadow-md transform-gpu
                                                                                                                                                                                                                                                before:absolute before:inset-0 before:bg-gradient-to-t before:from-transparent before:to-[rgba(229,183,105,0.10)] before:rounded-lg
                                                                                                                                                                                                                                                before:pointer-events-none before:opacity-80
                                                                                                                                                                                                                                                after:absolute after:inset-0 after:rounded-lg after:shadow-[inset_0_1px_2px_rgba(255,255,255,0.15),0_3px_8px_rgba(0,0,0,0.4)]
                                                                                                                                                                                                                                                after:pointer-events-none">

                        
                        <div
                            class="absolute top-[6px] left-3 right-3 h-[1px] bg-gradient-to-r from-transparent via-[#e5b769] to-transparent opacity-60 pointer-events-none">
                        </div>
                        <div
                            class="absolute bottom-[6px] left-3 right-3 h-[1px] bg-gradient-to-r from-transparent via-[#e5b769] to-transparent opacity-60 pointer-events-none">
                        </div>

                        <div class="flex items-center justify-center">
                            
                            <?php if($paginatedItems->onFirstPage()): ?>
                                <button disabled
                                    class="w-10 h-10 flex items-center justify-center rounded border border-[#3d3a32] bg-[#1a1814] text-[#514b3c] cursor-not-allowed opacity-70 mr-2 pointer-events-none">
                                    <span class="text-lg font-medium">←</span>
                                </button>
                            <?php else: ?>
                                <a href="<?php echo e($paginatedItems->previousPageUrl()); ?>"
                                    class="w-10 h-10 flex items-center justify-center rounded border border-[#514b3c] bg-[#252117] hover:bg-[#2d2820] hover:border-[#a6925e] transition-all duration-300 text-[#a6925e] mr-2 hover:text-[#e5b769] pointer-events-auto">
                                    <span class="text-lg font-medium">←</span>
                                </a>
                            <?php endif; ?>

                            
                            <div class="flex items-center justify-center">
                                <?php $__currentLoopData = $paginatedItems->getUrlRange(1, $paginatedItems->lastPage()); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $page => $url): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <a href="<?php echo e($url); ?>"
                                                        class="relative w-10 h-10 flex items-center justify-center mx-1 rounded <?php echo e($page == $paginatedItems->currentPage()
                                    ? 'border border-[#e5b769] bg-[#2d2820] text-[#e5b769] font-medium shadow-[inset_0_1px_3px_rgba(0,0,0,0.3)] pointer-events-auto'
                                    : 'border border-[#514b3c] bg-[#252117] text-[#a6925e] hover:bg-[#2d2820] hover:border-[#a6925e] hover:text-[#e5b769] pointer-events-auto'); ?>

                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            transition-colors duration-300">
                                                        <?php if($page == $paginatedItems->currentPage()): ?>
                                                            
                                                            <div
                                                                class="absolute top-0 left-0 w-[5px] h-[5px] border-t border-l border-[#e5b769] opacity-80 pointer-events-none">
                                                            </div>
                                                            <div
                                                                class="absolute top-0 right-0 w-[5px] h-[5px] border-t border-r border-[#e5b769] opacity-80 pointer-events-none">
                                                            </div>
                                                            <div
                                                                class="absolute bottom-0 left-0 w-[5px] h-[5px] border-b border-l border-[#e5b769] opacity-80 pointer-events-none">
                                                            </div>
                                                            <div
                                                                class="absolute bottom-0 right-0 w-[5px] h-[5px] border-b border-r border-[#e5b769] opacity-80 pointer-events-none">
                                                            </div>
                                                            <span class="text-lg"
                                                                style="text-shadow: 0 0 4px rgba(229, 183, 105, 0.5);"><?php echo e($page); ?></span>
                                                        <?php else: ?>
                                                            <span class="text-lg"><?php echo e($page); ?></span>
                                                        <?php endif; ?>
                                                    </a>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>

                            
                            <?php if($paginatedItems->hasMorePages()): ?>
                                <a href="<?php echo e($paginatedItems->nextPageUrl()); ?>"
                                    class="w-10 h-10 flex items-center justify-center rounded border border-[#514b3c] bg-[#252117] hover:bg-[#2d2820] hover:border-[#a6925e] transition-all duration-300 text-[#a6925e] ml-2 hover:text-[#e5b769] pointer-events-auto">
                                    <span class="text-lg font-medium">→</span>
                                </a>
                            <?php else: ?>
                                <button disabled
                                    class="w-10 h-10 flex items-center justify-center rounded border border-[#3d3a32] bg-[#1a1814] text-[#514b3c] cursor-not-allowed opacity-70 ml-2 pointer-events-none">
                                    <span class="text-lg font-medium">→</span>
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            
            <div class="text-center mb-4">
                <div class="inline-block px-3 py-1 bg-[#1a1814] rounded border-t border-b border-[#514b3c]">
                    <span class="text-[#9a9483] text-xs">
                        Страница <span class="text-[#e5b769]"><?php echo e($paginatedItems->currentPage()); ?></span> из <span
                            class="text-[#e5b769]"><?php echo e($paginatedItems->lastPage()); ?></span>
                        <span class="inline-block mx-1 text-[#514b3c]">•</span>
                        Всего: <span class="text-[#e5b769]"><?php echo e($totalItemCount); ?></span>
                    </span>
                </div>
            </div>
        <?php endif; ?>


    </div> 

    
    <div class="container mx-auto text-center px-2 py-2 flex justify-center space-x-2">
        
        <a href="<?php echo e(route('inventory.index')); ?>"
            class="bg-[#c4a76d] text-[#2f2d2b] py-1 px-1 rounded shadow-lg hover:bg-[#d4b781] transition duration-300">Рюкзак</a>
        
        <a href="<?php echo e(route('user.profile')); ?>"
            class="bg-[#c4a76d] text-[#2f2d2b] py-1 px-1 rounded shadow-lg hover:bg-[#d4b781] transition duration-300">Персонаж</a>
        
        <a href="#"
            class="bg-[#c4a76d] text-[#2f2d2b] py-1 px-1 rounded shadow-lg hover:bg-[#d4b781] transition duration-300">Гильдия</a>
    </div>
    
    <?php if(Auth::check()): ?> 
        
        <?php if (isset($component)) { $__componentOriginal4766510e0268a7a5917e77b146281554 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4766510e0268a7a5917e77b146281554 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.footer','data' => ['onlineCount' => $onlineCount]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.footer'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['onlineCount' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($onlineCount)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4766510e0268a7a5917e77b146281554)): ?>
<?php $attributes = $__attributesOriginal4766510e0268a7a5917e77b146281554; ?>
<?php unset($__attributesOriginal4766510e0268a7a5917e77b146281554); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4766510e0268a7a5917e77b146281554)): ?>
<?php $component = $__componentOriginal4766510e0268a7a5917e77b146281554; ?>
<?php unset($__componentOriginal4766510e0268a7a5917e77b146281554); ?>
<?php endif; ?>
    <?php endif; ?>

    
    <div id="itemDetailsModal"
        class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-60 hidden">
        <div
            class="relative bg-[#2f2d2b] rounded-lg shadow-lg max-w-lg w-full border-2 border-[#a6925e] overflow-y-auto max-h-[90vh]">

            
            <div id="itemDetailsModalContent" class="p-4">
                <div class="text-center text-[#e5b769]">Загрузка...</div>
            </div>
        </div>
    </div>

    
    <script>
        // Открыть модалку и загрузить детали предмета по AJAX
        function showItemDetailsModal(shopItemId) {
            const modal = document.getElementById('itemDetailsModal');
            const content = document.getElementById('itemDetailsModalContent');
            modal.classList.remove('hidden');
            content.innerHTML = '<div class="text-center text-[#e5b769]">Загрузка...</div>';

            // AJAX-запрос к контроллеру для получения partial
            fetch('/shop/item-details/' + shopItemId)
                .then(response => {
                    // Проверяем, что ответ успешный
                    if (!response.ok) {
                        throw new Error('Ошибка сети: ' + response.status);
                    }
                    // Проверяем, что ответ в формате JSON
                    const contentType = response.headers.get('content-type');
                    if (!contentType || !contentType.includes('application/json')) {
                        throw new Error('Получен неверный формат данных');
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.html) {
                        content.innerHTML = data.html;
                    } else if (data.error) {
                        content.innerHTML = `<div class="text-center py-4">
                            <div class="text-red-500 text-lg mb-2">Произошла ошибка при загрузке информации о предмете.</div>
                            <div class="text-[#d9d3b8] text-sm">${data.error}</div>
                            <button onclick="closeItemDetailsModal()" class="mt-4 px-4 py-2 bg-[#38352c] text-[#e5b769] rounded border border-[#514b3c] hover:bg-[#4a452c]">
                                Закрыть
                            </button>
                        </div>`;
                    } else {
                        content.innerHTML = '<div class="text-red-500 text-center py-4">Произошла ошибка при загрузке информации о предмете.</div>';
                    }
                })
                .catch(error => {
                    console.error('Ошибка при загрузке информации о предмете:', error);
                    content.innerHTML = `<div class="text-center py-4">
                        <div class="text-red-500 text-lg mb-2">Произошла ошибка при загрузке информации о предмете.</div>
                        <div class="text-[#d9d3b8] text-sm">${error.message}</div>
                        <button onclick="closeItemDetailsModal()" class="mt-4 px-4 py-2 bg-[#38352c] text-[#e5b769] rounded border border-[#514b3c] hover:bg-[#4a452c]">
                            Закрыть
                        </button>
                    </div>`;
                });
        }

        // Закрыть модалку
        function closeItemDetailsModal() {
            document.getElementById('itemDetailsModal').classList.add('hidden');
        }

        // Закрытие по клику вне окна
        window.addEventListener('click', function (event) {
            const modal = document.getElementById('itemDetailsModal');
            if (event.target === modal) {
                closeItemDetailsModal();
            }
        });
    </script>
</body>

</html><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/shop/index.blade.php ENDPATH**/ ?>
/**
 * Алхимическая лаборатория - JavaScript функциональность
 * Обрабатывает выбор рецептов, варку зелий, проверку статуса и улучшение зелий
 */

// Глобальные переменные
let selectedRecipeId = null;
let selectedRecipeData = null;
let selectedPotionId = null;
let selectedCatalystId = null;

// Функции для работы с localStorage
function saveSelectedRecipe(recipeId) {
    if (recipeId) {
        localStorage.setItem('alchemist_selected_recipe', recipeId);
        console.log('Сохранен выбранный рецепт в localStorage:', recipeId);
    } else {
        localStorage.removeItem('alchemist_selected_recipe');
        console.log('Удален выбранный рецепт из localStorage');
    }
}

function loadSelectedRecipe() {
    const savedRecipeId = localStorage.getItem('alchemist_selected_recipe');
    if (savedRecipeId) {
        console.log('Загружен выбранный рецепт из localStorage:', savedRecipeId);
        return savedRecipeId;
    }
    return null;
}

// Функция для выбора рецепта из списка
function selectRecipe(recipeId) {
    console.log('Выбран рецепт с ID:', recipeId);

    // Выделяем визуально выбранный рецепт
    const recipeElements = document.querySelectorAll('.recipe-card');
    recipeElements.forEach(el => {
        el.classList.remove('ring-2', 'ring-[#a6925e]', 'bg-gradient-to-b', 'from-[#3d3a2e]', 'to-[#2a2721]');
        el.classList.add('bg-gradient-to-b', 'from-[#2a2722]', 'to-[#1d1916]');
    });

    const selectedElement = document.querySelector(`.recipe-card[data-recipe-id="${recipeId}"]`);
    if (selectedElement) {
        console.log('Найден элемент рецепта в DOM');
        selectedElement.classList.remove('bg-gradient-to-b', 'from-[#2a2722]', 'to-[#1d1916]');
        selectedElement.classList.add('ring-2', 'ring-[#a6925e]', 'bg-gradient-to-b', 'from-[#3d3a2e]', 'to-[#2a2721]');

        // Сохраняем ID выбранного рецепта
        selectedRecipeId = recipeId;
        saveSelectedRecipe(recipeId);

        // Получаем данные рецепта из атрибутов
        selectedRecipeData = {
            id: recipeId,
            name: selectedElement.dataset.recipeName,
            brewing_time: selectedElement.dataset.brewingTime,
            experience: selectedElement.dataset.experience,
            ingredients: JSON.parse(selectedElement.dataset.ingredients || '[]')
        };

        // Обновляем кнопки действий
        updateActionButtons();
    } else {
        console.error('Элемент рецепта не найден в DOM');
    }
}

// Функция для обновления состояния кнопок действий
function updateActionButtons() {
    const startBrewingBtn = document.getElementById('start-brewing-btn');
    if (startBrewingBtn) {
        startBrewingBtn.disabled = !selectedRecipeId;
    }
}

// Функция для начала варки зелья
function startBrewing() {
    if (!selectedRecipeId) {
        showErrorMessage('Сначала выберите рецепт из списка');
        return;
    }

    // Проверяем, что нет активной варки
    const activeBrewingElement = document.querySelector('#active-brewing');
    if (activeBrewingElement && activeBrewingElement.dataset.active === 'true') {
        showErrorMessage('У вас уже идет процесс варки зелья');
        return;
    }

    // Отображаем индикатор загрузки
    const brewButton = document.getElementById('start-brewing-btn');
    if (brewButton) {
        brewButton.disabled = true;
        brewButton.innerHTML = '<span class="flex items-center justify-center"><span class="mr-2">⏳</span><span>Начинаем варку...</span></span>';
        brewButton.classList.add('opacity-50');
    }

    // Подготавливаем данные для запроса
    const requestData = {
        recipe_id: selectedRecipeId
    };

    // Отправляем запрос на сервер для начала варки
    fetch('/masters/alchemist/start-brewing', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            'Accept': 'application/json'
        },
        body: JSON.stringify(requestData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Обновляем страницу для отображения активной варки
            window.location.reload();
        } else {
            // Восстанавливаем кнопку
            if (brewButton) {
                brewButton.innerHTML = '<span class="flex items-center justify-center"><span class="mr-2">🧪</span><span>Начать варку</span></span>';
                brewButton.disabled = false;
                brewButton.classList.remove('opacity-50');
            }
            // Показываем сообщение об ошибке
            showErrorMessage(data.message || 'Не удалось начать варку');
        }
    })
    .catch(error => {
        console.error('Ошибка:', error);
        // Восстанавливаем кнопку
        if (brewButton) {
            brewButton.innerHTML = '<span class="flex items-center justify-center"><span class="mr-2">🧪</span><span>Начать варку</span></span>';
            brewButton.disabled = false;
            brewButton.classList.remove('opacity-50');
        }
        showErrorMessage('Произошла ошибка при попытке начать варку');
    });
}

// Функция для сбора готовых зелий
function collectPotions() {
    const collectButton = document.getElementById('collect-potions-btn');
    if (collectButton) {
        collectButton.disabled = true;
        collectButton.innerHTML = '<span class="flex items-center justify-center"><span class="mr-2">⏳</span><span>Собираем...</span></span>';
        collectButton.classList.add('opacity-50');
    }

    fetch('/masters/alchemist/collect-potion', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            'Accept': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Обновляем страницу
            window.location.reload();
        } else {
            // Восстанавливаем кнопку
            if (collectButton) {
                collectButton.disabled = false;
                collectButton.innerHTML = '<span class="flex items-center justify-center"><span class="mr-2">✨</span><span>Собрать зелья</span></span>';
                collectButton.classList.remove('opacity-50');
            }
            showErrorMessage(data.message || 'Не удалось собрать зелья');
        }
    })
    .catch(error => {
        console.error('Ошибка:', error);
        // Восстанавливаем кнопку
        if (collectButton) {
            collectButton.disabled = false;
            collectButton.innerHTML = '<span class="flex items-center justify-center"><span class="mr-2">✨</span><span>Собрать зелья</span></span>';
            collectButton.classList.remove('opacity-50');
        }
        showErrorMessage('Произошла ошибка при попытке собрать зелья');
    });
}

// Функция для отмены варки
function cancelBrewing() {
    // Показываем модальное окно подтверждения
    const modal = document.getElementById('cancel-brewing-modal');
    if (modal) {
        modal.classList.remove('hidden');
    }
}

// Функция для подтверждения отмены варки
function confirmCancelBrewing() {
    const modal = document.getElementById('cancel-brewing-modal');
    if (modal) {
        modal.classList.add('hidden');
    }

    fetch('/masters/alchemist/cancel-brewing', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            'Accept': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Обновляем страницу
            window.location.reload();
        } else {
            showErrorMessage(data.message || 'Не удалось отменить варку');
        }
    })
    .catch(error => {
        console.error('Ошибка:', error);
        showErrorMessage('Произошла ошибка при попытке отменить варку');
    });
}

// Функция для закрытия модального окна отмены
function closeCancelModal() {
    const modal = document.getElementById('cancel-brewing-modal');
    if (modal) {
        modal.classList.add('hidden');
    }
}

// Функция для обновления статуса варки
function refreshStatus() {
    const refreshButton = document.getElementById('refresh-status-btn');
    if (refreshButton) {
        refreshButton.disabled = true;
        refreshButton.innerHTML = '<span class="flex items-center justify-center"><span class="mr-2">⏳</span><span class="hidden sm:inline">Обновляем...</span></span>';
    }

    fetch('/masters/alchemist/check-brewing-status', {
        method: 'GET',
        headers: {
            'Accept': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Обновляем страницу для отображения актуального статуса
            window.location.reload();
        } else {
            // Восстанавливаем кнопку
            if (refreshButton) {
                refreshButton.disabled = false;
                refreshButton.innerHTML = '<span class="flex items-center justify-center"><span class="mr-2">🔄</span><span class="hidden sm:inline">Обновить</span></span>';
            }
            showErrorMessage(data.message || 'Не удалось обновить статус');
        }
    })
    .catch(error => {
        console.error('Ошибка:', error);
        // Восстанавливаем кнопку
        if (refreshButton) {
            refreshButton.disabled = false;
            refreshButton.innerHTML = '<span class="flex items-center justify-center"><span class="mr-2">🔄</span><span class="hidden sm:inline">Обновить</span></span>';
        }
        showErrorMessage('Произошла ошибка при обновлении статуса');
    });
}

// Функция для отображения ошибки
function showErrorMessage(message) {
    const errorToast = document.createElement('div');
    errorToast.className = 'fixed top-4 left-1/2 transform -translate-x-1/2 bg-[#301a1a] border border-[#962c2c] text-[#e77c7c] px-4 py-2 rounded-md z-50 text-sm shadow-lg';
    errorToast.innerHTML = `<div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="12"></line><line x1="12" y1="16" x2="12.01" y2="16"></line></svg>${message}</div>`;
    document.body.appendChild(errorToast);

    // Скрываем через 5 секунд
    setTimeout(() => {
        errorToast.style.opacity = '0';
        errorToast.style.transition = 'opacity 0.5s';
        setTimeout(() => errorToast.remove(), 500);
    }, 5000);
}

// Функция для проверки статуса варки
function checkBrewingStatus() {
    const activeBrewingElement = document.querySelector('#active-brewing');
    
    // Проверяем, что котел активен и в нем нет готового зелья
    if (activeBrewingElement && activeBrewingElement.dataset.active === 'true' && activeBrewingElement.dataset.completed !== 'true') {
        fetch('/masters/alchemist/check-brewing-status')
            .then(response => {
                if (!response.ok) {
                    throw new Error('Ошибка при проверке статуса варки: ' + response.status);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // Обновляем время, если элемент существует
                    const timeLeftElement = document.getElementById('time-remaining');
                    if (timeLeftElement) {
                        if (data.completed) {
                            timeLeftElement.textContent = '0:00';
                        } else {
                            timeLeftElement.textContent = data.time_left_formatted || 'Загрузка...';
                        }
                    }

                    // Если варка завершена, обновляем страницу
                    if (data.completed) {
                        console.log('Варка завершена, обновляем страницу');
                        window.location.reload();
                    }
                }
            })
            .catch(error => {
                console.error('Ошибка при проверке статуса:', error);
            });
    }
}

// Инициализация при загрузке страницы
document.addEventListener('DOMContentLoaded', function () {
    console.log('Алхимическая лаборатория загружена');

    // Добавляем обработчики кликов для выбора рецептов
    const recipeCards = document.querySelectorAll('.recipe-card');
    console.log('Найдено карточек рецептов:', recipeCards.length);

    recipeCards.forEach(card => {
        card.addEventListener('click', function() {
            const recipeId = this.getAttribute('data-recipe-id');
            console.log('Клик по карточке рецепта с ID:', recipeId);
            selectRecipe(recipeId);
        });
    });

    // Добавляем обработчики для кнопок действий
    const startBrewingBtn = document.getElementById('start-brewing-btn');
    if (startBrewingBtn) {
        startBrewingBtn.addEventListener('click', startBrewing);
    }

    const collectPotionsBtn = document.getElementById('collect-potions-btn');
    if (collectPotionsBtn) {
        collectPotionsBtn.addEventListener('click', collectPotions);
    }

    const cancelBrewingBtn = document.getElementById('cancel-brewing-btn');
    if (cancelBrewingBtn) {
        cancelBrewingBtn.addEventListener('click', cancelBrewing);
    }

    const confirmCancelBtn = document.getElementById('confirm-cancel-brewing');
    if (confirmCancelBtn) {
        confirmCancelBtn.addEventListener('click', confirmCancelBrewing);
    }

    const closeCancelBtn = document.getElementById('close-cancel-modal');
    if (closeCancelBtn) {
        closeCancelBtn.addEventListener('click', closeCancelModal);
    }

    const refreshStatusBtn = document.getElementById('refresh-status-btn');
    if (refreshStatusBtn) {
        refreshStatusBtn.addEventListener('click', refreshStatus);
    }

    // Запускаем периодическую проверку статуса варки
    const activeBrewingElement = document.querySelector('#active-brewing');
    if (activeBrewingElement && activeBrewingElement.dataset.active === 'true' && activeBrewingElement.dataset.completed !== 'true') {
        console.log('Запускаем проверку статуса варки');
        // Проверяем статус каждые 10 секунд
        setInterval(checkBrewingStatus, 10000);
        // И сразу при загрузке страницы
        checkBrewingStatus();
    }

    // Загружаем сохраненный рецепт из localStorage
    const savedRecipeId = loadSelectedRecipe();
    if (savedRecipeId) {
        // Проверяем, существует ли рецепт с таким ID на странице
        const recipeElement = document.querySelector(`.recipe-card[data-recipe-id="${savedRecipeId}"]`);
        if (recipeElement) {
            console.log('Автоматически выбираем сохраненный рецепт:', savedRecipeId);
            selectRecipe(savedRecipeId);
        } else {
            console.log('Сохраненный рецепт не найден на странице, очищаем localStorage');
            saveSelectedRecipe(null);
        }
    }
});

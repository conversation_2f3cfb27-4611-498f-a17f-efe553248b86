<!DOCTYPE html>
<html lang="en">
@php use Illuminate\Support\Facades\Auth; @endphp

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    {{-- CSRF-токен для AJAX-запросов --}}
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Магазин - {{ Auth::check() ? Auth::user()->name : 'Гость' }}</title>

    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>

<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif min-h-screen flex flex-col">
    {{-- Основной контейнер --}}
    <div class="container max-w-md mx-auto px-1 py-0 border-2 border-[#a6925e] rounded-lg flex-grow">
        {{-- HP/MP блок с уведомлениями --}}
        <x-layout.hp-mp-bar :actualResources="$actualResources" :userProfile="$userProfile">
            {{-- Слот для уведомлений между HP и MP --}}
            <x-layout.notifications-bar :hasUnreadMessages="$hasUnreadMessages ?? false"
                :unreadMessagesCount="$unreadMessagesCount ?? 0" :hasBrokenItems="$hasBrokenItems ?? false"
                :brokenItemsCount="$brokenItemsCount ?? 0" />
        </x-layout.hp-mp-bar>

        {{-- Отображение валют --}}
        <x-layout.currency-display :userProfile="$userProfile" :experienceProgress="$experienceProgress ?? null" />

        {{-- Приветственное сообщение --}}
        <div class="text-center flex justify-center space-x-1 mt-2">
            @if (session('welcome_message'))
                <div class="bg-[#3b3a33] text-white p-4 rounded mb-4">
                    {{ session('welcome_message') }}
                </div>
            @endif
        </div>

        {{-- Хлебные крошки (без блока изображения) --}}
        <div class="w-full mx-auto">
            <x-breadcrumbs :breadcrumbs="$breadcrumbs ?? []" />
            {{-- Декоративная HR-линия --}}
            <div class="px-4 py-1">
                <hr class="border-0 h-px bg-gradient-to-r from-transparent via-[#e5b769] to-transparent opacity-70">
            </div>
            {{-- Название локации --}}
            <p
                class="block text-center mx-auto text-white py-1 px-8 rounded-sm shadow-lg bg-gradient-to-b from-[#2E8B57]">
                Магазин
            </p>
        </div>

        {{-- Здесь продолжайте использовать ваш основной код --}}
        <div class="container mx-auto px-4 py-6 bg-gradient-to-b from-[#4a4a3d] to-[#3b3a33]">



            {{-- Фильтры --}}
            <div class="flex flex-wrap justify-between mb-4 gap-1">
                <div class="container mx-auto px-7 py-2 bg-gradient-to-b from-[#4a4a3d] to-[#3b3a33]">
                    {{-- Фильтры --}}
                    <form id="filterForm" method="GET" action="">
                        <div class="flex flex-wrap justify-between gap-1">
                            {{-- Фильтр по типу --}}
                            <div class="flex flex-col w-full sm:w-auto">
                                <label for="itemType" class="text-sm font-medium text-[#d9d3b8] mb-1">Тип:</label>
                                <select id="itemType" name="type"
                                    class="bg-[#6b6658] text-[#f5f5f5] py-1 px-3 rounded-md text-xs focus:outline-none focus:ring-2 focus:ring-[#807c70]">
                                    <option value="">Все</option>
                                    <option value="weapon">Оружие</option>
                                    <option value="helmet">Шлем</option>
                                    <option value="armor">Броня</option>
                                    <option value="shield">Щит</option>
                                </select>
                            </div>

                            {{-- Фильтр по качеству --}}
                            <div class="flex flex-col w-full sm:w-auto">
                                <label for="quality" class="text-sm font-medium text-[#d9d3b8] mb-1">Качество:</label>
                                <select id="quality" name="quality"
                                    class="bg-[#6b6658] text-[#f5f5f5] py-1 px-3 rounded-md text-xs focus:outline-none focus:ring-2 focus:ring-[#807c70]">
                                    <option value="">Все</option>
                                    <option value="common">Обычное</option>
                                    <option value="rare">Редкое</option>
                                    <option value="magic">Магическое</option>
                                    <option value="epic">Эпическое</option>
                                    <option value="unique">Уникальное</option>
                                    <option value="divine">Божественное</option>
                                </select>
                            </div>

                            {{-- Фильтр по атрибуту --}}
                            <div class="flex flex-col w-full sm:w-auto">
                                <label for="attribute" class="text-sm font-medium text-[#d9d3b8] mb-1">Атрибут:</label>
                                <select id="attribute" name="attribute"
                                    class="bg-[#6b6658] text-[#f5f5f5] py-1 px-3 rounded-md text-xs focus:outline-none focus:ring-2 focus:ring-[#807c70]">
                                    <option value="">Все</option>
                                    <option value="hp">ХП</option>
                                    <option value="mp">МП</option>
                                    <option value="strength">Сила</option>
                                    <option value="intelligence">Интелект</option>
                                </select>
                            </div>

                            {{-- Фильтр по цене --}}
                            <div class="flex flex-col w-full sm:w-auto">
                                <label for="price" class="text-sm font-medium text-[#d9d3b8] mb-1">Цена:</label>
                                <select id="price" name="price"
                                    class="bg-[#6b6658] text-[#f5f5f5] py-1 px-3 rounded-md text-xs focus:outline-none focus:ring-2 focus:ring-[#807c70]">
                                    <option value="">Все</option>
                                    <option value="bronze">Бронза</option>
                                    <option value="silver">Серебро</option>
                                    <option value="gold">Золото</option>
                                </select>
                            </div>
                        </div>

                        {{-- Кнопки --}}
                        <div class="flex justify-center gap-2 mt-4">
                            <button type="submit"
                                class="bg-[#c4a76d] text-[#2f2d2b] py-2 px-6 rounded hover:bg-[#d4b781] transition duration-300">
                                Применить
                            </button>
                            <button type="reset"
                                class="bg-[#6b6658] text-[#f5f5f5] py-2 px-1 rounded hover:bg-[#807c70] transition duration-300">
                                Сбросить
                            </button>
                        </div>
                    </form>
                </div>

                <div class="flex flex-wrap  justify-center gap-4 mt-2">
                    {{-- Карточка предмета --}}

                    <div class="bg-[#3b3a33]  border border-[#a6925e] rounded-lg p-4 shadow-lg w-60">
                        {{-- Верхняя часть: иконка + название/уровень --}}
                        <div class="flex items-center mb-4">
                            {{-- Иконка предмета --}}
                            <div class="relative">
                                <img src="{{ asset('assets/placeholder.png') }}" alt="Иконка предмета"
                                    class="w-12 h-12 object-cover border border-[#a6925e] rounded">
                                {{-- Улучшение под иконкой --}}
                                <p class="text-xs font-bold text-green-400 absolute -bottom-4 left-0">
                                    (+15111)
                                </p>
                            </div>
                            {{-- Название + цена + уровень --}}
                            <div class="ml-3 leading-tight">
                                <h2 class="text-sm font-bold text-[#e5b769]">
                                    Меч Скитальца
                                </h2>
                                <p class="text-sm text-[#d9d3b8]">Цена: 2 бронзы</p>
                                <p class="text-sm text-[#d9d3b8]">5 ур.</p>
                            </div>
                        </div>
                        {{-- Кнопки --}}
                        <div class="flex justify-between space-x-2">
                            <button
                                class="bg-[#c4a76d] text-[#2f2d2b] px-4 py-2 rounded hover:bg-[#d4b781] transition duration-300">
                                Купить
                            </button>
                            <button
                                class="bg-[#c4a76d] text-[#2f2d2b] px-4 py-2 rounded hover:bg-[#d4b781] transition duration-300">
                                Подробнее
                            </button>
                        </div>
                    </div>
                    <div class="bg-[#3b3a33] border border-[#a6925e] rounded-lg p-4 shadow-lg w-60">
                        {{-- Верхняя часть: иконка + название/уровень --}}
                        <div class="flex items-center mb-4">
                            {{-- Иконка предмета --}}
                            <div class="relative">
                                <img src="{{ asset('assets/placeholder.png') }}" alt="Иконка предмета"
                                    class="w-12 h-12 object-cover border border-[#a6925e] rounded">
                                {{-- Улучшение под иконкой --}}
                                <p class="text-xs font-bold text-green-400 absolute -bottom-4 left-0">
                                    (+15111)
                                </p>
                            </div>
                            {{-- Название + цена + уровень --}}
                            <div class="ml-3 leading-tight">
                                <h2 class="text-sm font-bold text-[#e5b769]">
                                    Меч Скитальца
                                </h2>
                                <p class="text-sm text-[#d9d3b8]">Цена: 2 бронзы</p>
                                <p class="text-sm text-[#d9d3b8]">5 ур.</p>
                            </div>
                        </div>
                        {{-- Кнопки --}}
                        <div class="flex justify-between space-x-2">
                            <button
                                class="bg-[#c4a76d] text-[#2f2d2b] px-4 py-2 rounded hover:bg-[#d4b781] transition duration-300">
                                Купить
                            </button>
                            <button
                                class="bg-[#c4a76d] text-[#2f2d2b] px-4 py-2 rounded hover:bg-[#d4b781] transition duration-300">
                                Подробнее
                            </button>
                        </div>
                    </div>
                    <div class="bg-[#3b3a33] border border-[#a6925e] rounded-lg p-4 shadow-lg w-60">
                        {{-- Верхняя часть: иконка + название/уровень --}}
                        <div class="flex items-center mb-4">
                            {{-- Иконка предмета --}}
                            <div class="relative">
                                <img src="{{ asset('assets/placeholder.png') }}" alt="Иконка предмета"
                                    class="w-12 h-12 object-cover rounded animate-breathing-glow glow-color-gray-200">
                                {{-- Улучшение под иконкой --}}
                                <p class="text-xs font-bold text-green-400 absolute -bottom-4 left-0">
                                    (+15111)
                                </p>
                            </div>
                            {{-- Название + цена + уровень --}}
                            <div class="ml-3 leading-tight">
                                <h2 class="text-sm font-bold text-[#e5b769]">
                                    Меч Скитальца
                                </h2>
                                <p class="text-sm text-[#d9d3b8]">Цена: 2 бронзы</p>
                                <p class="text-sm text-[#d9d3b8]">5 ур.</p>
                            </div>
                        </div>
                        {{-- Кнопки --}}
                        <div class="flex justify-between space-x-2">
                            <button
                                class="bg-[#c4a76d] text-[#2f2d2b] px-4 py-2 rounded hover:bg-[#d4b781] transition duration-300">
                                Купить
                            </button>
                            <button
                                class="bg-[#c4a76d] text-[#2f2d2b] px-4 py-2 rounded hover:bg-[#d4b781] transition duration-300">
                                Подробнее
                            </button>
                        </div>
                    </div>


                    <div class="bg-[#3b3a33] border border-[#a6925e] rounded-lg p-4 shadow-lg w-60">
                        {{-- Верхняя часть: иконка + название/уровень --}}
                        <div class="flex items-center mb-4">
                            {{-- Иконка предмета --}}
                            <div class="relative">
                                <img src="{{ asset('assets/placeholder.png') }}" alt="Иконка предмета"
                                    class="w-12 h-12 object-cover rounded animate-breathing-glow glow-color-gray-200">
                                {{-- Улучшение под иконкой --}}
                                <p class="text-xs font-bold text-green-400 absolute -bottom-4 left-0">
                                    (+1511)
                                </p>
                            </div>
                            {{-- Название + цена + уровень --}}
                            <div class="ml-3 leading-tight">
                                <h2 class="text-sm font-bold text-[#e5b769]">
                                    Меч Скитальца
                                </h2>
                                <p class="text-sm text-[#d9d3b8]">Цена: 2 бронзы</p>
                                <p class="text-sm text-[#d9d3b8]">5 ур.</p>
                            </div>
                        </div>
                        {{-- Кнопки --}}
                        <div class="flex justify-between space-x-2">
                            <button
                                class="bg-[#c4a76d] text-[#2f2d2b] px-4 py-2 rounded hover:bg-[#d4b781] transition duration-300">
                                Купить
                            </button>
                            <button
                                class="bg-[#c4a76d] text-[#2f2d2b] px-4 py-2 rounded hover:bg-[#d4b781] transition duration-300">
                                Подробнее
                            </button>
                        </div>
                    </div>


                    <div class="bg-[#3b3a33] border border-[#a6925e] rounded-lg p-4 shadow-lg w-60">
                        {{-- Верхняя часть: иконка + название/уровень --}}
                        <div class="flex items-center mb-4">
                            {{-- Иконка предмета --}}
                            <div class="relative">
                                <img src="{{ asset('assets/placeholder.png') }}" alt="Иконка предмета"
                                    class="w-12 h-12 object-cover border border-[#a6925e] rounded">
                                {{-- Улучшение под иконкой --}}
                                <p class="text-xs font-bold text-green-400 absolute -bottom-4 left-0">
                                    (+1511)
                                </p>
                            </div>
                            {{-- Название + цена + уровень --}}
                            <div class="ml-3 leading-tight">
                                <h2 class="text-sm font-bold text-[#e5b769]">
                                    Меч Скитальца
                                </h2>
                                <p class="text-sm text-[#d9d3b8]">Цена: 2 бронзы</p>
                                <p class="text-sm text-[#d9d3b8]">5 ур.</p>
                            </div>
                        </div>
                        {{-- Кнопки --}}
                        <div class="flex justify-between space-x-2">
                            <button
                                class="bg-[#c4a76d] text-[#2f2d2b] px-4 py-2 rounded hover:bg-[#d4b781] transition duration-300">
                                Купить
                            </button>
                            <button
                                class="bg-[#c4a76d] text-[#2f2d2b] px-4 py-2 rounded hover:bg-[#d4b781] transition duration-300">
                                Подробнее
                            </button>
                        </div>
                    </div>
                    {{-- Пагинация --}}
                    <div class="mt-6 text-center mb-4">
                        <button class="bg-[#c4a76d] text-[#2f2d2b] py-2 px-4 rounded hover:bg-[#d4b781]">Пред.</button>
                        <span class="text-[#e5b769] mx-2">|1|</span>
                        <span class="text-[#e5b769] mx-2">2</span>
                        <span class="text-[#e5b769] mx-2">3</span>
                        <button class="bg-[#c4a76d] text-[#2f2d2b] py-2 px-4 rounded hover:bg-[#d4b781]">След.</button>
                    </div>

                </div>
            </div>
        </div>
    </div>

    {{-- Нижние кнопки навигации --}}
    <x-layout.navigation-buttons />

    {{-- Футер --}}
    <x-layout.footer :onlineCount="$onlineCount ?? 0" />
</body>

</html>
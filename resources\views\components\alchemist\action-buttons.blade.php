@props(['activeBrewing' => null, 'completedPotions' => [], 'selectedRecipeId' => null])

{{--
Компонент кнопок действий алхимика
Отображает кнопки для начала варки, сбора зелий и других действий
--}}

<div class="mb-4">
    <div class="flex flex-col sm:flex-row gap-3 justify-center">
        
        {{-- Кнопка начала варки --}}
        @if(!isset($activeBrewing) && (!isset($completedPotions) || count($completedPotions) == 0))
            <button id="start-brewing-btn" 
                class="bg-gradient-to-b from-[#4a452c] to-[#2a2721] text-[#e5b769] px-6 py-3 rounded-md border border-[#8c784e] hover:from-[#5a552c] hover:to-[#3a3721] hover:text-[#f0d89e] hover:border-[#a6925e] transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:from-[#4a452c] disabled:hover:to-[#2a2721] font-medium"
                {{ !$selectedRecipeId ? 'disabled' : '' }}>
                <span class="flex items-center justify-center">
                    <span class="mr-2">🧪</span>
                    <span>Начать варку</span>
                </span>
            </button>
        @endif
        
        {{-- Кнопка сбора готовых зелий --}}
        @if(isset($completedPotions) && count($completedPotions) > 0)
            <button id="collect-potions-btn" 
                class="bg-gradient-to-b from-[#2f473c] to-[#1e2e27] text-[#f8eac2] px-6 py-3 rounded-md border border-[#3e5c48] hover:from-[#3c5c48] hover:to-[#243b2f] hover:border-[#4a6b54] transition-all duration-300 font-medium">
                <span class="flex items-center justify-center">
                    <span class="mr-2">✨</span>
                    <span>Собрать зелья ({{ count($completedPotions) }})</span>
                </span>
            </button>
        @endif
        
        {{-- Кнопка отмены варки (если процесс активен) --}}
        @if(isset($activeBrewing))
            <button id="cancel-brewing-btn" 
                class="bg-gradient-to-b from-[#59372d] to-[#3c221b] text-[#f8eac2] px-6 py-3 rounded-md border border-[#6e3f35] hover:from-[#704135] hover:to-[#4a2b22] hover:border-[#8a4d42] transition-all duration-300 font-medium">
                <span class="flex items-center justify-center">
                    <span class="mr-2">❌</span>
                    <span>Отменить варку</span>
                </span>
            </button>
        @endif
        
        {{-- Кнопка обновления статуса --}}
        <button id="refresh-status-btn" 
            class="bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] text-[#d3c6a6] px-4 py-3 rounded-md border border-[#514b3c] hover:from-[#4a452c] hover:to-[#3a3721] hover:text-[#e5b769] hover:border-[#8c784e] transition-all duration-300">
            <span class="flex items-center justify-center">
                <span class="mr-2">🔄</span>
                <span class="hidden sm:inline">Обновить</span>
            </span>
        </button>
    </div>
    
    {{-- Информационные сообщения --}}
    <div class="mt-3 text-center">
        {{-- Сообщение о выборе рецепта --}}
        @if(!$selectedRecipeId && !isset($activeBrewing) && (!isset($completedPotions) || count($completedPotions) == 0))
            <div class="text-xs text-[#998d66] bg-[#1a1814] border border-[#3d3a2e] rounded px-3 py-2 inline-block">
                <span class="mr-1">💡</span>
                Выберите рецепт из списка для начала варки
            </div>
        @endif
        
        {{-- Сообщение о процессе варки --}}
        @if(isset($activeBrewing))
            <div class="text-xs text-[#7cfc00] bg-[#1a301a] border border-[#397239] rounded px-3 py-2 inline-block">
                <span class="mr-1">⏳</span>
                Зелье варится... Ожидайте завершения процесса
            </div>
        @endif
        
        {{-- Сообщение о готовых зельях --}}
        @if(isset($completedPotions) && count($completedPotions) > 0)
            <div class="text-xs text-[#ffd700] bg-[#3d3a2e] border border-[#a6925e] rounded px-3 py-2 inline-block">
                <span class="mr-1">✨</span>
                Зелья готовы! Соберите их в рюкзак
            </div>
        @endif
    </div>
    
    {{-- Скрытые поля для передачи данных в JavaScript --}}
    <div id="brewing-data" class="hidden"
         data-selected-recipe="{{ $selectedRecipeId }}"
         data-has-active-brewing="{{ isset($activeBrewing) ? 'true' : 'false' }}"
         data-completed-potions-count="{{ isset($completedPotions) ? count($completedPotions) : 0 }}"
         data-csrf-token="{{ csrf_token() }}">
    </div>
</div>

{{-- Модальное окно подтверждения отмены --}}
<div id="cancel-brewing-modal" class="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50 hidden">
    <div class="bg-[#2f2d2b] border-2 border-[#a6925e] rounded-lg shadow-xl p-6 w-11/12 sm:w-auto sm:max-w-md mx-auto relative z-10 transform transition-all">
        <div class="text-center mb-6">
            <h3 class="text-2xl font-bold text-[#e5b769] mb-3">Отменить варку?</h3>
            <p class="text-[#d9d3b8] text-base leading-relaxed">
                Вы уверены, что хотите отменить текущий процесс варки? 
                Все ингредиенты будут потеряны, а прогресс сброшен.
            </p>
        </div>
        
        <div class="flex space-x-3 justify-center">
            {{-- Кнопка подтверждения --}}
            <button id="confirm-cancel-brewing" 
                class="bg-gradient-to-b from-[#59372d] to-[#3c221b] text-[#f8eac2] px-6 py-2 rounded-md border border-[#6e3f35] hover:from-[#704135] hover:to-[#4a2b22] transition-all duration-300 font-medium">
                Да, отменить
            </button>
            
            {{-- Кнопка отмены --}}
            <button id="close-cancel-modal" 
                class="bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] text-[#d3c6a6] px-6 py-2 rounded-md border border-[#514b3c] hover:from-[#4a452c] hover:to-[#3a3721] hover:text-[#e5b769] transition-all duration-300 font-medium">
                Отмена
            </button>
        </div>
    </div>
</div>

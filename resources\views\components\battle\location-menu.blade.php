{{--
Компонент для отображения меню боевых локаций
--}}

@props(['isStunned' => false])

@php
    // Проверяем, заблокированы ли рудники для текущего пользователя
    $locationAccessService = app(\App\Services\LocationAccessService::class);
    $user = Auth::user();
    $isMinesBlocked = $user ? $locationAccessService->isLocationBlocked('Рудники', $user) : false;
@endphp

<div class="mb-3">
    <div
        class="relative bg-gradient-to-b from-[#2a2722] via-[#1f1c17] to-[#1a1814] rounded-lg shadow-[0_8px_32px_rgba(0,0,0,0.6),inset_0_1px_0_rgba(193,169,110,0.1)] overflow-hidden transform transition-all duration-300 hover:shadow-[0_12px_40px_rgba(0,0,0,0.7),inset_0_1px_0_rgba(193,169,110,0.15)]">
        {{-- Декоративная подсветка сверху --}}
        <div
            class="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-[#c1a96e] to-transparent opacity-40">
        </div>

        {{-- Заголовок секции с объемным эффектом --}}
        <div
            class="relative bg-gradient-to-b from-[#3b3629] via-[#2f2a1e] to-[#2a2722] py-3 px-4 shadow-[inset_0_1px_0_rgba(193,169,110,0.2),inset_0_-1px_0_rgba(0,0,0,0.3)]">
            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-[#c1a96e] to-transparent opacity-5">
            </div>
            <h3
                class="relative text-center text-[#e4d7b0] font-bold text-sm tracking-wider uppercase drop-shadow-[0_2px_4px_rgba(0,0,0,0.8)] filter drop-shadow-[0_0_8px_rgba(193,169,110,0.3)]">
                Боевые локации
            </h3>
        </div>

        {{-- Аванпосты --}}
        <x-battle.location-item route="{{ route('battle.outposts.index') }}" icon="assets/iconOutpost.png"
            title="Аванпосты" :isActive="true" :isStunned="$isStunned" />

        {{-- Рудники --}}
        <x-battle.location-item route="{{ route('battle.mines.index') }}" icon="assets/iconMines.png" title="Рудники"
            :isActive="!$isMinesBlocked" :isBlocked="$isMinesBlocked" :isStunned="$isStunned" />

        {{-- Подземелья --}}
        <x-battle.location-item route="{{ route('dungeons.index') }}" icon="assets/iconDungeon.png" title="Подземелья"
            :isActive="true" :isStunned="$isStunned" />

        {{-- Декоративный разделитель с объемным эффектом --}}
        <div
            class="relative px-4 py-3 bg-gradient-to-r from-[#1a1814] via-[#1f1c17] to-[#1a1814] shadow-[inset_0_2px_4px_rgba(0,0,0,0.4)]">
            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-[#2a2722] to-transparent opacity-30">
            </div>
            <div class="relative">
                <div
                    class="h-px bg-gradient-to-r from-transparent via-[#c1a96e] to-transparent opacity-80 shadow-[0_0_4px_rgba(193,169,110,0.4)]">
                </div>
                <div class="text-center mt-2">
                    <span
                        class="inline-block px-3 py-1 bg-gradient-to-b from-[#2a2722] to-[#1a1814] text-[#c1a96e] text-xs font-semibold tracking-wide rounded-sm shadow-[0_2px_4px_rgba(0,0,0,0.5),inset_0_1px_0_rgba(193,169,110,0.1)] border border-[#3b3629]">
                        Скоро доступно
                    </span>
                </div>
            </div>
        </div>

        {{-- Испытание --}}
        <x-battle.location-item route="#" icon="assets/iconTrial.png" title="Испытание" :isActive="false"
            badge="Скоро" onClick="showMobileTooltip('trial-tooltip')" :isStunned="$isStunned" />

        {{-- Доминионы --}}
        <x-battle.location-item route="#" icon="assets/iconDominions.png" title="Доминионы" :isActive="false"
            badge="Скоро" onClick="showMobileTooltip('dominions-tooltip')" :isStunned="$isStunned" />

        {{-- Временные события --}}
        <x-battle.location-item route="#" icon="assets/iconTemporary.png" title="Временные событие" :isActive="false"
            badge="Скоро" onClick="showMobileTooltip('events-tooltip')" :isStunned="$isStunned" />
    </div>
</div>
{{--
Компонент для отображения меню боевых локаций
--}}

@props(['isStunned' => false])

@php
    // Проверяем, заблокированы ли рудники для текущего пользователя
    $locationAccessService = app(\App\Services\LocationAccessService::class);
    $user = Auth::user();
    $isMinesBlocked = $user ? $locationAccessService->isLocationBlocked('Рудники', $user) : false;
@endphp

<div class="mb-3">
    <div
        class="bg-gradient-to-b from-[#2a2722] to-[#1d1916] border border-[#514b3c] rounded-md shadow-lg overflow-hidden">
        {{-- Заголовок секции --}}
        <div class="bg-gradient-to-r from-[#3d3a2e] to-[#2a2721] py-2 px-3 border-b border-[#514b3c]">
            <h3
                class="text-center text-[#e4d7b0] font-semibold text-sm tracking-wider uppercase drop-shadow-[0_1px_2px_rgba(0,0,0,0.8)]">
                ⚔️ Боевые локации ⚔️
            </h3>
        </div>

        {{-- Аванпосты --}}
        <x-battle.location-item route="{{ route('battle.outposts.index') }}" icon="assets/iconOutpost.png"
            title="Аванпосты" :isActive="true" :isStunned="$isStunned" />

        {{-- Рудники --}}
        <x-battle.location-item route="{{ route('battle.mines.index') }}" icon="assets/iconMines.png" title="Рудники"
            :isActive="!$isMinesBlocked" :isBlocked="$isMinesBlocked" :isStunned="$isStunned" />

        {{-- Подземелья --}}
        <x-battle.location-item route="{{ route('dungeons.index') }}" icon="assets/iconDungeon.png" title="Подземелья"
            :isActive="true" :isStunned="$isStunned" />

        {{-- Декоративный разделитель внутри меню --}}
        <div class="px-3 py-1 bg-gradient-to-r from-[#1d1916] to-[#2a2722]">
            <div class="h-px bg-gradient-to-r from-transparent via-[#c1a96e] to-transparent opacity-60 shadow-sm"></div>
            <div class="text-center mt-1">
                <span class="text-[#998d66] text-xs font-medium tracking-wide">Скоро доступно</span>
            </div>
        </div>

        {{-- Испытание --}}
        <x-battle.location-item route="#" icon="assets/iconTrial.png" title="Испытание" :isActive="false"
            badge="В разработке" onClick="showMobileTooltip('trial-tooltip')" :isStunned="$isStunned" />

        {{-- Доминионы --}}
        <x-battle.location-item route="#" icon="assets/iconDominions.png" title="Доминионы" :isActive="false"
            badge="В разработке" onClick="showMobileTooltip('dominions-tooltip')" :isStunned="$isStunned" />

        {{-- Временные события --}}
        <x-battle.location-item route="#" icon="assets/iconTemporary.png" title="Временные событие" :isActive="false"
            badge="В разработке" onClick="showMobileTooltip('events-tooltip')" :isStunned="$isStunned" />
    </div>
</div>
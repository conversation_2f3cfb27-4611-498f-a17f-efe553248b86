@props([
    'route' => '#',
    'icon' => '',
    'title' => '',
    'isActive' => true,
    'isBlocked' => false,
    'badge' => null,
    'tooltipId' => null,
    'onClick' => null,
    'isStunned' => false
])

{{--
Компонент для отображения элемента боевой локации
Принимает:
- route: маршрут для перехода
- icon: путь к иконке
- title: название локации
- isActive: активна ли локация (по умолчанию true)
- isBlocked: заблокирована ли локация (по умолчанию false)
- badge: текст бейджа (например, "В разработке")
- tooltipId: ID для всплывающей подсказки
- onClick: JavaScript функция для клика (для неактивных элементов)
--}}

@if($isBlocked)
    {{-- Заблокированная локация --}}
    <div class="relative flex items-center text-left text-[#998d66] pl-0 pr-3 bg-gradient-to-r from-[#1d1916] via-[#1a1814] to-[#2a2722] shadow-[inset_0_2px_4px_rgba(0,0,0,0.4)] overflow-hidden transition-all duration-300">
        <span
            class="w-12 h-12 flex items-center justify-center mr-0 text-[#998d66] bg-gradient-to-br from-[#2a2722] via-[#1f1c17] to-[#1d1916] shadow-[inset_1px_0_0_rgba(59,54,41,0.6),inset_0_1px_0_rgba(153,141,102,0.1)] relative">
            <img src="{{ asset($icon) }}" alt="{{ $title }}"
                class="w-8 h-8 filter grayscale opacity-40 drop-shadow-[0_1px_2px_rgba(0,0,0,0.8)]">
            {{-- Иконка блокировки с объемным эффектом --}}
            <div class="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-br from-[#6e3f35] via-[#5a3329] to-[#3c221b] rounded-full shadow-[0_2px_4px_rgba(0,0,0,0.6),inset_0_1px_0_rgba(248,234,194,0.1)] flex items-center justify-center border border-[#8b4a3a]">
                <span class="text-[#f8eac2] text-xs drop-shadow-[0_1px_1px_rgba(0,0,0,0.8)]">🔒</span>
            </div>
        </span>
        <span class="font-medium tracking-wide drop-shadow-[0_1px_2px_rgba(0,0,0,0.8)] ml-3 opacity-75">{{ $title }}</span>

        {{-- Статус "На доработке" с улучшенным дизайном --}}
        <span class="ml-auto flex items-center space-x-2">
            <div class="w-2 h-2 bg-gradient-to-r from-[#c1a96e] to-[#998d66] rounded-full animate-pulse shadow-[0_0_6px_rgba(193,169,110,0.5)]"></div>
            <span class="inline-block px-2 py-1 bg-gradient-to-b from-[#2a2722] to-[#1a1814] text-[#c1a96e] text-xs font-semibold tracking-wide rounded-sm shadow-[0_2px_4px_rgba(0,0,0,0.5),inset_0_1px_0_rgba(193,169,110,0.1)] border border-[#3b3629]">На доработке</span>
        </span>

        {{-- Полупрозрачный оверлей --}}
        <div class="absolute inset-0 bg-gradient-to-r from-transparent via-black/10 to-transparent pointer-events-none"></div>

        {{-- Диагональные полосы --}}
        <div class="absolute inset-0 opacity-5 pointer-events-none"
             style="background-image: repeating-linear-gradient(
                 45deg,
                 transparent,
                 transparent 10px,
                 rgba(193, 169, 110, 0.3) 10px,
                 rgba(193, 169, 110, 0.3) 20px
             );">
        </div>
    </div>
@elseif($isActive)
    <a href="{{ $isStunned ? '#' : $route }}"
        class="flex items-center text-left text-[#d4cbb0] pl-0 pr-3 bg-gradient-to-r from-[#1d1916] via-[#211f1a] to-[#2a2722] shadow-[inset_0_1px_0_rgba(193,169,110,0.05)] transition-all duration-300 hover:from-[#2a2722] hover:via-[#2f2d28] hover:to-[#3b3629] hover:text-[#e4d7b0] hover:shadow-[inset_0_0_12px_rgba(193,169,110,0.15),0_2px_4px_rgba(0,0,0,0.3)] {{ $isStunned ? 'opacity-40 grayscale cursor-not-allowed pointer-events-none' : '' }}"
        {{ $isStunned ? 'onclick="event.preventDefault(); return false;"' : '' }}>
        <span
            class="w-12 h-12 flex items-center justify-center mr-0 text-[#c1a96e] bg-gradient-to-br from-[#2a2722] via-[#241f1b] to-[#1d1916] shadow-[inset_1px_0_0_rgba(59,54,41,0.8),inset_0_1px_0_rgba(193,169,110,0.1)] transition-all duration-300 hover:shadow-[inset_1px_0_0_rgba(59,54,41,0.9),inset_0_1px_0_rgba(193,169,110,0.15),0_0_8px_rgba(193,169,110,0.4)]">
            <img src="{{ asset($icon) }}" alt="{{ $title }}"
                class="w-8 h-8 filter drop-shadow-[0_0_4px_rgba(193,169,110,0.5)] transition-all duration-300 hover:drop-shadow-[0_0_8px_rgba(193,169,110,0.7)] hover:scale-105">
        </span>
        <span
            class="font-medium tracking-wide drop-shadow-[0_1px_2px_rgba(0,0,0,0.8)] ml-3 transition-all duration-300">{{ $title }}</span>
    </a>
@else
    <div class="flex items-center text-left text-[#998d66] pl-0 pr-3 bg-gradient-to-r from-[#1d1916] via-[#1a1814] to-[#2a2722] shadow-[inset_0_1px_0_rgba(59,54,41,0.3)] relative group md:cursor-default cursor-pointer transition-all duration-300 hover:from-[#2a2722] hover:via-[#241f1b] hover:to-[#1d1916] hover:text-[#c1a96e] hover:shadow-[inset_0_0_8px_rgba(193,169,110,0.1)]"
        @if($onClick) onclick="{{ $onClick }}" @endif>
        <span
            class="w-12 h-12 flex items-center justify-center mr-0 text-[#998d66] bg-gradient-to-br from-[#2a2722] via-[#1f1c17] to-[#1d1916] shadow-[inset_1px_0_0_rgba(59,54,41,0.6),inset_0_1px_0_rgba(153,141,102,0.1)] transition-all duration-300 group-hover:text-[#c1a96e] group-hover:shadow-[inset_1px_0_0_rgba(59,54,41,0.8),inset_0_1px_0_rgba(193,169,110,0.15)]">
            <img src="{{ asset($icon) }}" alt="{{ $title }}"
                class="w-8 h-8 filter grayscale opacity-60 drop-shadow-[0_1px_2px_rgba(0,0,0,0.8)] transition-all duration-300 group-hover:opacity-80 group-hover:drop-shadow-[0_0_4px_rgba(193,169,110,0.3)]">
        </span>
        <span
            class="font-medium tracking-wide drop-shadow-[0_1px_2px_rgba(0,0,0,0.8)] ml-3 transition-all duration-300">{{ $title }}</span>

        @if($badge)
            <span class="ml-auto">
                <span
                    class="inline-flex items-center px-3 py-1 rounded-md bg-gradient-to-b from-[#6e3f35] via-[#5a3329] to-[#3c221b] text-[#f8eac2] text-xs relative overflow-hidden shadow-[0_3px_6px_rgba(0,0,0,0.6),inset_0_1px_0_rgba(248,234,194,0.1)] transition-all duration-300 group-hover:shadow-[0_4px_8px_rgba(110,63,53,0.4),inset_0_1px_0_rgba(248,234,194,0.15)] border border-[#8b4a3a]">
                    <span class="relative z-10 font-semibold drop-shadow-[0_1px_2px_rgba(0,0,0,0.8)] tracking-wide">{{ $badge }}</span>
                    <span
                        class="absolute inset-0 bg-gradient-to-r from-[#6e3f35] via-[#7a4b3f] to-[#6e3f35] opacity-20 animate-pulse"></span>
                    {{-- Декоративная подсветка --}}
                    <span class="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-[#f8eac2] to-transparent opacity-30"></span>
                </span>
            </span>
        @endif
    </div>
@endif

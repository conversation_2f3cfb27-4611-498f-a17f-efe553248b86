@props([
    'route' => '#',
    'icon' => '',
    'title' => '',
    'isActive' => true,
    'isBlocked' => false,
    'badge' => null,
    'tooltipId' => null,
    'onClick' => null,
    'isStunned' => false
])

{{--
Компонент для отображения элемента боевой локации
Принимает:
- route: маршрут для перехода
- icon: путь к иконке
- title: название локации
- isActive: активна ли локация (по умолчанию true)
- isBlocked: заблокирована ли локация (по умолчанию false)
- badge: текст бейджа (например, "В разработке")
- tooltipId: ID для всплывающей подсказки
- onClick: JavaScript функция для клика (для неактивных элементов)
--}}

@if($isBlocked)
    {{-- Заблокированная локация --}}
    <div class="relative flex items-center text-left text-[#998d66] pl-0 pr-3 bg-gradient-to-r from-[#1d1916] to-[#2a2722] border-b border-[#3b3629] overflow-hidden transition-all duration-300">
        <span
            class="w-12 h-12 flex items-center justify-center mr-0 text-[#998d66] bg-gradient-to-br from-[#2a2722] to-[#1d1916] border-r border-[#3b3629] relative">
            <img src="{{ asset($icon) }}" alt="{{ $title }}"
                class="w-8 h-8 filter grayscale opacity-40 drop-shadow-[0_1px_2px_rgba(0,0,0,0.8)]">
            {{-- Иконка блокировки --}}
            <div class="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-br from-[#6e3f35] to-[#3c221b] rounded-full border border-[#998d66] flex items-center justify-center">
                <span class="text-[#f8eac2] text-xs">🔒</span>
            </div>
        </span>
        <span class="font-medium tracking-wide drop-shadow-[0_1px_2px_rgba(0,0,0,0.8)] ml-3 opacity-75">{{ $title }}</span>

        {{-- Статус "На доработке" --}}
        <span class="ml-auto flex items-center space-x-2">
            <div class="w-2 h-2 bg-gradient-to-r from-[#c1a96e] to-[#998d66] rounded-full animate-pulse shadow-[0_0_4px_rgba(193,169,110,0.4)]"></div>
            <span class="text-[#c1a96e] text-xs font-medium drop-shadow-[0_1px_1px_rgba(0,0,0,0.8)]">На доработке</span>
        </span>

        {{-- Полупрозрачный оверлей --}}
        <div class="absolute inset-0 bg-gradient-to-r from-transparent via-black/10 to-transparent pointer-events-none"></div>

        {{-- Диагональные полосы --}}
        <div class="absolute inset-0 opacity-5 pointer-events-none"
             style="background-image: repeating-linear-gradient(
                 45deg,
                 transparent,
                 transparent 10px,
                 rgba(193, 169, 110, 0.3) 10px,
                 rgba(193, 169, 110, 0.3) 20px
             );">
        </div>
    </div>
@elseif($isActive)
    <a href="{{ $isStunned ? '#' : $route }}"
        class="flex items-center text-left text-[#d4cbb0] pl-0 pr-3 bg-gradient-to-r from-[#1d1916] to-[#2a2722] border-b border-[#3b3629] transition-all duration-300 hover:from-[#2a2722] hover:to-[#3b3629] hover:text-[#e4d7b0] hover:shadow-[inset_0_0_10px_rgba(193,169,110,0.2)] {{ $isStunned ? 'opacity-40 grayscale cursor-not-allowed pointer-events-none' : '' }}"
        {{ $isStunned ? 'onclick="event.preventDefault(); return false;"' : '' }}>
        <span
            class="w-12 h-12 flex items-center justify-center mr-0 text-[#c1a96e] bg-gradient-to-br from-[#2a2722] to-[#1d1916] border-r border-[#3b3629] transition-all duration-300 hover:shadow-[0_0_8px_rgba(193,169,110,0.4)]">
            <img src="{{ asset($icon) }}" alt="{{ $title }}"
                class="w-8 h-8 filter drop-shadow-[0_0_4px_rgba(193,169,110,0.5)] transition-all duration-300 hover:drop-shadow-[0_0_6px_rgba(193,169,110,0.7)]">
        </span>
        <span
            class="font-medium tracking-wide drop-shadow-[0_1px_2px_rgba(0,0,0,0.8)] ml-3 transition-all duration-300">{{ $title }}</span>
    </a>
@else
    <div class="flex items-center text-left text-[#998d66] pl-0 pr-3 bg-gradient-to-r from-[#1d1916] to-[#2a2722] border-b border-[#3b3629] relative group md:cursor-default cursor-pointer transition-all duration-300 hover:from-[#2a2722] hover:to-[#1d1916] hover:text-[#c1a96e]"
        @if($onClick) onclick="{{ $onClick }}" @endif>
        <span
            class="w-12 h-12 flex items-center justify-center mr-0 text-[#998d66] bg-gradient-to-br from-[#2a2722] to-[#1d1916] border-r border-[#3b3629] transition-all duration-300 group-hover:text-[#c1a96e]">
            <img src="{{ asset($icon) }}" alt="{{ $title }}"
                class="w-8 h-8 filter grayscale opacity-60 drop-shadow-[0_1px_2px_rgba(0,0,0,0.8)] transition-all duration-300 group-hover:opacity-80">
        </span>
        <span
            class="font-medium tracking-wide drop-shadow-[0_1px_2px_rgba(0,0,0,0.8)] ml-3 transition-all duration-300">{{ $title }}</span>

        @if($badge)
            <span class="ml-auto">
                <span
                    class="inline-flex items-center px-2 py-1 rounded-sm bg-gradient-to-r from-[#2f473c] to-[#1e2e27] text-[#f8eac2] text-xs border border-[#3b3629] relative overflow-hidden shadow-[0_2px_4px_rgba(0,0,0,0.5)] transition-all duration-300 group-hover:shadow-[0_0_8px_rgba(193,169,110,0.3)]">
                    <span class="relative z-10 font-medium drop-shadow-[0_1px_1px_rgba(0,0,0,0.8)]">{{ $badge }}</span>
                    <span
                        class="absolute inset-0 bg-gradient-to-r from-[#2f473c] via-[#3b4a3f] to-[#2f473c] opacity-30 animate-pulse"></span>
                </span>
            </span>
        @endif
    </div>
@endif

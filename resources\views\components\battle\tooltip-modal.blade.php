@props([
    'id' => '',
    'title' => '',
    'icon' => '',
    'description' => '',
    'releaseDate' => '',
    'requirements' => [],
    'plannedEvents' => null
])

{{--
Компонент для отображения всплывающих подсказок
Принимает:
- id: уникальный ID модального окна
- title: заголовок
- icon: путь к иконке
- description: описание
- releaseDate: дата выхода
- requirements: массив требований
- plannedEvents: количество планируемых событий (для временных событий)
--}}

<div id="{{ $id }}" class="fixed inset-0 bg-black bg-opacity-85 z-50 hidden items-center justify-center backdrop-blur-sm">
    <div
        class="relative w-11/12 max-w-sm mx-auto bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] border-2 border-[#514b3c] rounded-lg shadow-2xl overflow-hidden animate-fade-in">
        {{-- Заголовок --}}
        <div
            class="bg-gradient-to-r from-[#2a2722] to-[#3d3a2e] py-3 px-4 border-b border-[#514b3c] flex items-center justify-between">
            <div class="flex items-center">
                <img src="{{ asset($icon) }}" alt="{{ $title }}"
                    class="w-7 h-7 mr-3 filter drop-shadow-[0_0_4px_rgba(193,169,110,0.5)] transition-all duration-300">
                <h4 class="text-[#e4d7b0] text-base font-semibold tracking-wide drop-shadow-[0_1px_2px_rgba(0,0,0,0.8)]">{{ $title }}</h4>
            </div>
            <button type="button" onclick="closeMobileTooltip('{{ $id }}')"
                class="bg-gradient-to-br from-[#6e3f35] to-[#3c221b] text-[#f8eac2] hover:from-[#3c221b] hover:to-[#6e3f35] text-lg w-7 h-7 flex items-center justify-center rounded-full border border-[#3b3629] transition-all duration-300 hover:shadow-[0_0_8px_rgba(193,169,110,0.4)]">
                &times;
            </button>
        </div>

        <div class="p-4">
            {{-- Статус разработки --}}
            <div
                class="inline-flex items-center px-3 py-1.5 mb-3 rounded-md bg-gradient-to-r from-[#2f473c] to-[#1e2e27] text-[#f8eac2] text-xs border border-[#3b3629] relative overflow-hidden shadow-[0_2px_4px_rgba(0,0,0,0.5)]">
                <span class="relative z-10 font-medium drop-shadow-[0_1px_1px_rgba(0,0,0,0.8)]">В разработке</span>
                <span
                    class="absolute inset-0 bg-gradient-to-r from-[#2f473c] via-[#3b4a3f] to-[#2f473c] opacity-30 animate-pulse"></span>
            </div>

            {{-- Описание --}}
            <div class="bg-gradient-to-b from-[#1a1814] to-[#2a2722] border border-[#3b3629] rounded-md p-3 mb-3 shadow-inner">
                <p class="text-sm text-[#d4cbb0] leading-relaxed drop-shadow-[0_1px_1px_rgba(0,0,0,0.8)]">
                    {{ $description }}
                </p>
            </div>

            {{-- Информация о выходе --}}
            <div class="bg-gradient-to-b from-[#1a1814] to-[#2a2722] border border-[#3b3629] rounded-md p-3 @if(!empty($requirements)) mb-3 @endif shadow-inner">
                <div class="flex items-start mb-2">
                    <span class="text-[#c1a96e] text-sm mr-2 drop-shadow-[0_0_4px_rgba(193,169,110,0.4)]">⏱</span>
                    <div class="text-sm text-[#e4d7b0] italic font-medium drop-shadow-[0_1px_1px_rgba(0,0,0,0.8)]">{{ $releaseDate }}</div>
                </div>
                @if($plannedEvents)
                    <div class="flex items-start">
                        <span class="text-[#c1a96e] text-sm mr-2 drop-shadow-[0_0_4px_rgba(193,169,110,0.4)]">📅</span>
                        <div class="text-sm text-[#998d66] drop-shadow-[0_1px_1px_rgba(0,0,0,0.8)]">Планируемые события: {{ $plannedEvents }}</div>
                    </div>
                @endif
            </div>

            {{-- Требования (если есть) --}}
            @if(!empty($requirements))
                <div class="bg-gradient-to-b from-[#1a1814] to-[#2a2722] border border-[#6e3f35] rounded-md p-3 shadow-inner">
                    <div class="text-[#c1a96e] text-sm font-semibold mb-2 drop-shadow-[0_1px_1px_rgba(0,0,0,0.8)]">Требования:</div>
                    @foreach($requirements as $requirement)
                        <div class="flex items-start mb-1.5">
                            <span class="text-[#c1a96e] text-sm mr-2 drop-shadow-[0_0_4px_rgba(193,169,110,0.4)]">{{ $requirement['icon'] }}</span>
                            <div class="text-sm text-[#998d66] drop-shadow-[0_1px_1px_rgba(0,0,0,0.8)]">{{ $requirement['text'] }}</div>
                        </div>
                    @endforeach
                </div>
            @endif
        </div>
    </div>
</div>

/**
 * Адаптивные стили для страницы алхимика (/masters/alchemist)
 * Улучшение отображения ингредиентов на больших экранах
 */

/* Базовые стили для ингредиентов */
.ingredient-card {
    transition: all 0.3s ease;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    min-height: 120px;
}

.ingredient-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(193, 169, 110, 0.2);
}

/* Стили для больших экранов (lg: 1024px+) */
@media (min-width: 1024px) {
    .ingredient-card {
        padding: 0.75rem;
    }

    .ingredient-icon {
        width: 2.5rem;
        height: 2.5rem;
    }

    .ingredient-name {
        font-size: 0.875rem;
        line-height: 1.25rem;
    }

    .ingredient-quantity {
        font-size: 0.75rem;
    }
}

/* Стили для очень больших экранов (xl: 1280px+) */
@media (min-width: 1280px) {
    .ingredient-card {
        padding: 1rem;
    }

    .ingredient-icon {
        width: 2.75rem;
        height: 2.75rem;
    }

    .ingredient-name {
        font-size: 0.875rem;
    }

    .ingredient-quantity {
        font-size: 0.75rem;
    }

    /* Улучшение прогресс-бара */
    .ingredient-progress {
        height: 0.5rem;
    }
}

/* Стили для сверхбольших экранов (2xl: 1536px+) */
@media (min-width: 1536px) {
    .ingredient-card {
        padding: 1.25rem;
    }

    .ingredient-icon {
        width: 3rem;
        height: 3rem;
    }

    .ingredient-name {
        font-size: 1rem;
    }

    .ingredient-quantity {
        font-size: 0.875rem;
    }

    .ingredient-progress {
        height: 0.625rem;
    }
}

/* Стили для контейнера алхимика - убираем ограничения, используем Tailwind классы */

/* Улучшение отображения рецептов на больших экранах */
@media (min-width: 1024px) {
    .recipe-card {
        transition: all 0.3s ease;
    }

    .recipe-card:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(193, 169, 110, 0.15);
    }
}

/* Стили для панели ингредиентов */
@media (min-width: 1024px) {
    .ingredients-panel {
        max-height: 300px;
    }
}

@media (min-width: 1280px) {
    .ingredients-panel {
        max-height: 350px;
    }
}

@media (min-width: 1536px) {
    .ingredients-panel {
        max-height: 400px;
    }
}

/* Оптимизация для очень широких экранов */
@media (min-width: 1920px) {
    .ingredient-card {
        padding: 1.5rem;
    }

    .ingredient-icon {
        width: 3.5rem;
        height: 3.5rem;
    }

    .ingredient-name {
        font-size: 1.125rem;
    }

    .ingredient-quantity {
        font-size: 1rem;
    }
}

/* Сохранение компактности на мобильных устройствах */
@media (max-width: 640px) {
    .ingredient-card {
        padding: 0.5rem;
        min-height: 90px;
    }

    .ingredient-icon {
        width: 1.75rem;
        height: 1.75rem;
    }

    .ingredient-name {
        font-size: 0.75rem;
        line-height: 1rem;
    }

    .ingredient-quantity {
        font-size: 0.625rem;
    }

    .ingredient-progress {
        height: 0.375rem;
    }
}

/* Стили для улучшения видимости иконок */
.ingredient-icon-container {
    background: linear-gradient(135deg, #1a1814, #252117);
    border: 1px solid #514b3c;
    border-radius: 0.375rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.ingredient-icon-container:hover {
    border-color: #a6925e;
    box-shadow: 0 0 8px rgba(166, 146, 94, 0.3);
}

.ingredient-icon-container img {
    object-fit: contain;
    max-width: 100%;
    max-height: 100%;
}

/* Улучшение читаемости текста на больших экранах */
@media (min-width: 1024px) {
    .ingredient-text {
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    }
}

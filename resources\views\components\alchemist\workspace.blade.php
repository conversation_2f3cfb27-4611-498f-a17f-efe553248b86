@props(['activeBrewing' => null, 'completedPotions' => [], 'userProfile'])

{{--
Компонент рабочего стола алхимика
Отображает котёл с текущим процессом варки и готовые зелья
--}}

<div class="mb-4">
    <h3 class="text-[#e5b769] font-semibold text-base mb-2 border-b border-[#3d3a2e] pb-1">Рабочий стол алхимика</h3>

    {{-- Рабочий стол алхимика --}}
    <div class="grid grid-cols-1 md:grid-cols-2 gap-3 mb-3">
        {{-- Котёл с текущим процессом --}}
        <div id="active-brewing"
            class="bg-gradient-to-b from-[#2a2722] to-[#1d1916] border border-[#514b3c] rounded-md p-2"
            data-active="{{ isset($activeBrewing) || (isset($completedPotions) && count($completedPotions) > 0) ? 'true' : 'false' }}"
            data-completed="{{ (isset($completedPotions) && count($completedPotions) > 0) ? 'true' : 'false' }}">
            <div class="flex items-center justify-between mb-2">
                <span class="text-[#d3c6a6] text-sm font-medium">Котёл</span>
                @if(isset($activeBrewing))
                    <span class="text-[#7cfc00] text-xs px-2 py-0.5 bg-[#1a301a] rounded border border-[#397239]">
                        Варится
                    </span>
                @elseif(isset($completedPotions) && count($completedPotions) > 0)
                    <span class="text-[#ffd700] text-xs px-2 py-0.5 bg-[#3d3a2e] rounded border border-[#a6925e]">
                        Готово
                    </span>
                @else
                    <span class="text-[#8a8a8a] text-xs px-2 py-0.5 bg-[#2a2722] rounded border border-[#514b3c]">
                        Пусто
                    </span>
                @endif
            </div>

            {{-- Содержимое котла --}}
            <div class="bg-[#1a1814] rounded border border-[#3d3a2e] p-2 min-h-[80px] flex items-center justify-center">
                @if(isset($activeBrewing))
                    {{-- Активный процесс варки --}}
                    <div class="text-center">
                        <div class="text-2xl mb-1">🧪</div>
                        <div class="text-[#d3c6a6] text-xs">{{ $activeBrewing->recipe->name ?? 'Неизвестное зелье' }}</div>
                        <div class="text-[#7cfc00] text-xs mt-1" id="brewing-timer">
                            Осталось: <span id="time-remaining">Загрузка...</span>
                        </div>
                    </div>
                @elseif(isset($completedPotions) && count($completedPotions) > 0)
                    {{-- Готовые зелья --}}
                    <div class="text-center">
                        <div class="text-2xl mb-1">✨</div>
                        <div class="text-[#ffd700] text-xs">Готово зелий: {{ count($completedPotions) }}</div>
                        <button id="collect-all-potions"
                            class="mt-2 bg-gradient-to-b from-[#4a452c] to-[#2a2721] text-[#e5b769] text-xs px-3 py-1 rounded border border-[#8c784e] hover:from-[#5a552c] hover:to-[#3a3721] transition-all duration-300">
                            Собрать все
                        </button>
                    </div>
                @else
                    {{-- Пустой котёл --}}
                    <div class="text-center text-[#8a8a8a]">
                        <div class="text-2xl mb-1">🍯</div>
                        <div class="text-xs">Котёл пуст</div>
                        <div class="text-xs mt-1">Выберите рецепт для варки</div>
                    </div>
                @endif
            </div>
        </div>

        {{-- Информация о процессе --}}
        <div class="bg-gradient-to-b from-[#2a2722] to-[#1d1916] border border-[#514b3c] rounded-md p-2">
            <div class="flex items-center justify-between mb-2">
                <span class="text-[#d3c6a6] text-sm font-medium">Информация</span>
            </div>
            
            <div class="bg-[#1a1814] rounded border border-[#3d3a2e] p-2 min-h-[80px]">
                @if(isset($activeBrewing))
                    {{-- Информация об активном процессе --}}
                    <div class="text-xs text-[#d3c6a6] space-y-1">
                        <div><span class="text-[#e5b769]">Рецепт:</span> {{ $activeBrewing->recipe->name ?? 'Неизвестно' }}</div>
                        <div><span class="text-[#e5b769]">Время варки:</span> {{ $activeBrewing->recipe->brewing_time ?? 0 }} мин</div>
                        <div><span class="text-[#e5b769]">Опыт:</span> +{{ $activeBrewing->recipe->experience_reward ?? 0 }}</div>
                    </div>
                @elseif(isset($completedPotions) && count($completedPotions) > 0)
                    {{-- Информация о готовых зельях --}}
                    <div class="text-xs text-[#d3c6a6] space-y-1">
                        @foreach($completedPotions as $potion)
                            <div class="flex justify-between">
                                <span>{{ $potion->name ?? 'Зелье' }}</span>
                                <span class="text-[#ffd700]">x{{ $potion->quantity ?? 1 }}</span>
                            </div>
                        @endforeach
                    </div>
                @else
                    {{-- Общая информация --}}
                    <div class="text-xs text-[#8a8a8a] space-y-1">
                        <div>• Выберите рецепт из списка</div>
                        <div>• Убедитесь в наличии ингредиентов</div>
                        <div>• Нажмите "Начать варку"</div>
                        <div class="mt-2 text-[#d3c6a6]">
                            <span class="text-[#e5b769]">Уровень алхимии:</span> {{ $userProfile->alchemy_level ?? 1 }}
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

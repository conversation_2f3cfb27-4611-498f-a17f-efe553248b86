/* Импорт стилей для адаптивности страницы рынка */
@import "market-responsive.css";
/* Импорт стилей для мобильной оптимизации страницы слотов рынка */
@import "market-slots-mobile.css";
/* Импорт стилей для анимации свечения предметов */
@import "glow-effects.css";
/* Русский комментарий: Импорт стилей для системы пожертвований */
@import "donations.css";
/* Русский комментарий: Импорт стилей для системы уведомлений */
@import "notifications.css";
/* Русский комментарий: Импорт адаптивных стилей для алхимика */
@import "alchemist-responsive.css";

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Анимации для заглушек "В разработке" */
.animation-delay-300 {
    animation-delay: 300ms;
}

.animation-delay-700 {
    animation-delay: 700ms;
}

/* Улучшенные стили для фонового изображения названий локаций */
.location-name {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    margin: 0 auto;
    color: rgb(136, 141, 75);
    font-weight: 500;
    border-radius: 0.125rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
        0 2px 4px -1px rgba(0, 0, 0, 0.06);

    /* Улучшение четкости пиксельной графики */
    image-rendering: -webkit-optimize-contrast;
    image-rendering: -moz-crisp-edges;
    image-rendering: crisp-edges;
    image-rendering: pixelated;

    /* Читаемость текста */
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8), 1px 1px 2px rgba(0, 0, 0, 0.9);

    /* Базовые размеры */
    min-height: 32px;
    padding: 4px 16px;
    font-size: 0.875rem;
    line-height: 1.25rem;
}

/* Фоновое изображение через псевдоэлемент для лучшего контроля */
.location-name::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url("/assets/UI/locationName.png");
    background-position: center center;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    border-radius: inherit;
    z-index: 0;

    /* Улучшение четкости фонового изображения */
    image-rendering: -webkit-optimize-contrast;
    image-rendering: -moz-crisp-edges;
    image-rendering: crisp-edges;
    image-rendering: pixelated;
}

/* Контент поверх фона */
.location-name span {
    position: relative;
    z-index: 2;
    display: inline-block;
}

/* Адаптивные стили для мобильных устройств */
@media (max-width: 640px) {
    .location-name {
        font-size: 0.75rem;
        line-height: 1rem;
        padding: 3px 12px;
        min-height: 28px;
    }

    .location-name::before {
        /* На мобильных используем 100% 100% для заполнения всей ширины блока */
        background-size: 100% 100%;
        background-position: center center;
    }
}

@media (max-width: 480px) {
    .location-name {
        font-size: 1rem;
        padding: 2px 8px;
        min-height: 24px;
    }

    .location-name::before {
        background-size: 100% 100%;
    }
}

@media (max-width: 360px) {
    .location-name {
        font-size: 1rem;
        padding: 2px 6px;
        min-height: 22px;
    }

    .location-name::before {
        background-size: 100% 100%;
    }
}

/* Стили для больших экранов */
@media (min-width: 1024px) {
    .location-name {
        font-size: 1rem;
        line-height: 1.5rem;
        padding: 6px 20px;
        min-height: 36px;
    }

    .location-name::before {
        background-size: 100% 100%;
    }
}

/* Поддержка старых стилей для обратной совместимости */
p.location-name-bg {
    @apply location-name;
}

1753421857O:22:"App\Models\UserProfile":30:{s:13:" * connection";s:5:"pgsql";s:8:" * table";s:13:"user_profiles";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:7;s:7:"user_id";i:7;s:14:"inventory_used";i:3;s:18:"inventory_capacity";i:6;}s:11:" * original";a:4:{s:2:"id";i:7;s:7:"user_id";i:7;s:14:"inventory_used";i:3;s:18:"inventory_capacity";i:6;}s:10:" * changes";a:0:{}s:8:" * casts";a:3:{s:20:"last_regeneration_at";s:8:"datetime";s:11:"is_defeated";s:7:"boolean";s:11:"defeated_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:47:{i:0;s:7:"user_id";i:1;s:5:"level";i:2;s:10:"experience";i:3;s:18:"alchemy_experience";i:4;s:13:"alchemy_level";i:5;s:15:"potions_created";i:6;s:18:"testing_experience";i:7;s:13:"testing_level";i:8;s:8:"strength";i:9;s:9:"dexterity";i:10;s:12:"intelligence";i:11;s:8:"vitality";i:12;s:6:"max_hp";i:13;s:10:"current_hp";i:14;s:6:"max_mp";i:15;s:10:"current_mp";i:16;s:4:"gold";i:17;s:11:"crit_chance";i:18;s:11:"crit_damage";i:19;s:10:"magic_find";i:20;s:9:"gold_find";i:21;s:2:"gs";i:22;s:2:"GS";i:23;s:11:"stat_points";i:24;s:12:"skill_points";i:25;s:4:"race";i:26;s:5:"class";i:27;s:8:"recovery";i:28;s:5:"armor";i:29;s:15:"resistance_fire";i:30;s:20:"resistance_lightning";i:31;s:16:"resistanceNature";i:32;s:2:"hp";i:33;s:2:"mp";i:34;s:6:"silver";i:35;s:6:"bronze";i:36;s:9:"coinEvent";i:37;s:9:"coinGuild";i:38;s:11:"coinCrystal";i:39;s:10:"profession";i:40;s:18:"inventory_capacity";i:41;s:14:"inventory_used";i:42;s:20:"last_regeneration_at";i:43;s:11:"is_defeated";i:44;s:16:"defeated_by_type";i:45;s:14:"defeated_by_id";i:46;s:11:"defeated_at";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}
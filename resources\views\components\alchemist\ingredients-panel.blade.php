@props(['userIngredients' => [], 'userCatalysts' => []])

{{--
Компонент панели ингредиентов алхимика
Отображает доступные ингредиенты и катализаторы для алхимии
--}}

<div class="mb-4">
    <h3 class="text-[#e5b769] font-semibold text-base mb-2 border-b border-[#3d3a2e] pb-1">Ингредиенты и катализаторы</h3>
    
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {{-- Панель ингредиентов --}}
        <div class="bg-gradient-to-b from-[#2a2722] to-[#1d1916] border border-[#514b3c] rounded-md p-3">
            <h4 class="text-sm font-medium text-[#d3c6a6] mb-2 flex items-center">
                <span class="mr-2">🌿</span>
                Ингредиенты
            </h4>
            
            <div class="ingredients-panel bg-[#1a1814] rounded border border-[#3d3a2e] p-2 max-h-40 overflow-y-auto">
                @if($userIngredients && count($userIngredients) > 0)
                    <div class="space-y-2">
                        @foreach($userIngredients as $userIngredient)
                            @php $ingredient = $userIngredient->ingredient; @endphp
                            <div class="bg-[#252117] border border-[#3d3a2e] rounded p-2 text-center hover:bg-[#2a2722] transition-colors duration-200">
                                {{-- Иконка ингредиента --}}
                                @if($ingredient->icon_path)
                                    <img src="{{ asset($ingredient->icon_path) }}" 
                                         alt="{{ $ingredient->name }}" 
                                         class="w-8 h-8 mx-auto mb-1"
                                         onerror="this.src='{{ asset('assets/ingredients/default.png') }}'">
                                @else
                                    <div class="w-8 h-8 mx-auto mb-1 bg-[#3d3a2e] rounded flex items-center justify-center">
                                        <span class="text-xs">🌿</span>
                                    </div>
                                @endif
                                
                                {{-- Название и количество --}}
                                <div class="text-xs text-[#d3c6a6] truncate" title="{{ $ingredient->name }}">
                                    {{ $ingredient->name }}
                                </div>
                                <div class="text-xs text-[#c1a96e] font-medium">
                                    x{{ $userIngredient->quantity }}
                                </div>
                                
                                {{-- Редкость ингредиента --}}
                                @if($ingredient->rarity)
                                    <div class="text-xs mt-1">
                                        @switch($ingredient->rarity)
                                            @case('common')
                                                <span class="text-[#8a8a8a]">Обычный</span>
                                                @break
                                            @case('uncommon')
                                                <span class="text-[#7cfc00]">Необычный</span>
                                                @break
                                            @case('rare')
                                                <span class="text-[#1e90ff]">Редкий</span>
                                                @break
                                            @case('epic')
                                                <span class="text-[#9932cc]">Эпический</span>
                                                @break
                                            @case('legendary')
                                                <span class="text-[#ffd700]">Легендарный</span>
                                                @break
                                        @endswitch
                                    </div>
                                @endif
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center text-[#8a8a8a] py-4">
                        <div class="text-lg mb-1">🌿</div>
                        <div class="text-xs">Нет ингредиентов</div>
                        <div class="text-xs mt-1">Найдите ингредиенты в мире</div>
                    </div>
                @endif
            </div>
        </div>
        
        {{-- Панель катализаторов --}}
        <div class="bg-gradient-to-b from-[#2a2722] to-[#1d1916] border border-[#514b3c] rounded-md p-3">
            <h4 class="text-sm font-medium text-[#d3c6a6] mb-2 flex items-center">
                <span class="mr-2">💎</span>
                Катализаторы
            </h4>
            
            <div class="ingredients-panel bg-[#1a1814] rounded border border-[#3d3a2e] p-2 max-h-40 overflow-y-auto">
                @if($userCatalysts && count($userCatalysts) > 0)
                    <div class="space-y-2">
                        @foreach($userCatalysts as $userCatalyst)
                            @php $catalyst = $userCatalyst->catalyst; @endphp
                            <div class="bg-[#252117] border border-[#3d3a2e] rounded p-2 text-center hover:bg-[#2a2722] transition-colors duration-200">
                                {{-- Иконка катализатора --}}
                                @if($catalyst->icon_path)
                                    <img src="{{ asset($catalyst->icon_path) }}" 
                                         alt="{{ $catalyst->name }}" 
                                         class="w-8 h-8 mx-auto mb-1"
                                         onerror="this.src='{{ asset('assets/catalysts/default.png') }}'">
                                @else
                                    <div class="w-8 h-8 mx-auto mb-1 bg-[#3d3a2e] rounded flex items-center justify-center">
                                        <span class="text-xs">💎</span>
                                    </div>
                                @endif
                                
                                {{-- Название и количество --}}
                                <div class="text-xs text-[#d3c6a6] truncate" title="{{ $catalyst->name }}">
                                    {{ $catalyst->name }}
                                </div>
                                <div class="text-xs text-[#c1a96e] font-medium">
                                    x{{ $userCatalyst->quantity }}
                                </div>
                                
                                {{-- Эффект катализатора --}}
                                @if($catalyst->effect_description)
                                    <div class="text-xs text-[#998d66] mt-1 truncate" title="{{ $catalyst->effect_description }}">
                                        {{ $catalyst->effect_description }}
                                    </div>
                                @endif
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center text-[#8a8a8a] py-4">
                        <div class="text-lg mb-1">💎</div>
                        <div class="text-xs">Нет катализаторов</div>
                        <div class="text-xs mt-1">Катализаторы улучшают зелья</div>
                    </div>
                @endif
            </div>
        </div>
    </div>
    
    {{-- Информационная панель --}}
    <div class="mt-3 bg-[#1a1814] border border-[#3d3a2e] rounded p-2">
        <div class="text-xs text-[#998d66] space-y-1">
            <div class="flex items-center">
                <span class="text-[#c1a96e] mr-2">💡</span>
                <span>Ингредиенты необходимы для создания зелий по рецептам</span>
            </div>
            <div class="flex items-center">
                <span class="text-[#c1a96e] mr-2">✨</span>
                <span>Катализаторы могут улучшить качество и эффекты зелий</span>
            </div>
            <div class="flex items-center">
                <span class="text-[#c1a96e] mr-2">🔍</span>
                <span>Найти ингредиенты можно в различных локациях мира</span>
            </div>
        </div>
    </div>
</div>
